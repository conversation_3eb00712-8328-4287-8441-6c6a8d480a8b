import fs from "fs/promises"
import * as path from "path"
import * as vscode from "vscode"

import delay from "delay"

import { CommandExecutionStatus, DEFAULT_TERMINAL_OUTPUT_CHARACTER_LIMIT } from "@blue-ai-coder/types"
import { TelemetryService } from "@blue-ai-coder/telemetry"

import { Task } from "../task/Task"

import { ToolUse, AskApproval, HandleError, PushToolResult, RemoveClosingTag, ToolResponse } from "../../shared/tools"
import { formatResponse } from "../prompts/responses"
import { unescapeHtmlEntities } from "../../utils/text-normalization"
import { ExitCodeDetails, RooTerminalCallbacks, RooTerminalProcess } from "../../integrations/terminal/types"
import { TerminalRegistry } from "../../integrations/terminal/TerminalRegistry"
import { Terminal } from "../../integrations/terminal/Terminal"
import { SmartTerminalIntegration } from "../../services/smart-terminal-cache/terminal-integration"
import { Package } from "../../shared/package"
import { t } from "../../i18n"
import { AutonomousSupervisor } from "../../services/supervisor/AutonomousSupervisor"
import { DynamicTimeoutManager } from "./DynamicTimeoutManager"
import { InterceptionEngine } from "../../services/supervisor/InterceptionEngine"

class ShellIntegrationError extends Error {}

export async function executeCommandTool(
	task: Task,
	block: ToolUse,
	askApproval: AskApproval,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	removeClosingTag: RemoveClosingTag,
) {
	let command: string | undefined = block.params.command
	const customCwd: string | undefined = block.params.cwd

	try {
		if (block.partial) {
			await task.ask("command", removeClosingTag("command", command), block.partial).catch(() => {})
			return
		} else {
			if (!command) {
				task.consecutiveMistakeCount++
				task.recordToolError("execute_command")
				pushToolResult(await task.sayAndCreateMissingParamError("execute_command", "command"))
				return
			}

			const ignoredFileAttemptedToAccess = task.rooIgnoreController?.validateCommand(command)

			if (ignoredFileAttemptedToAccess) {
				await task.say("rooignore_error", ignoredFileAttemptedToAccess)
				pushToolResult(formatResponse.toolError(formatResponse.rooIgnoreError(ignoredFileAttemptedToAccess)))
				return
			}

			task.consecutiveMistakeCount = 0

			command = unescapeHtmlEntities(command) // Unescape HTML entities.

			// Supervisor integration - check command before approval
			const provider = await task.providerRef.deref()
			const outputChannel = provider?.getOutputChannel()
			if (outputChannel) {
				try {
					const supervisor = AutonomousSupervisor.getInstance(outputChannel)
					const interceptionEngine = new InterceptionEngine(outputChannel)

					// Monitor the command through supervisor
					const supervisorResult = await supervisor.monitorCommand(command, task)
					if (!supervisorResult.allowed) {
						// Command was blocked by supervisor
						pushToolResult(formatResponse.toolError(
							`🚫 **Command Blocked by Supervisor**\n\n` +
							`**Command:** \`${command}\`\n` +
							`**Reason:** ${supervisorResult.reason}\n\n` +
							`The supervisor prevents potentially unbounded processes to maintain session responsiveness. ` +
							`Please use safer alternatives or break the task into smaller operations.`
						))
						return
					}



					// Check for command modification
					if (supervisorResult.modifiedCommand && supervisorResult.modifiedCommand !== command) {
						await task.say("text",
							`⚠️ **Command Modified by Supervisor**\n\n` +
							`**Original:** \`${command}\`\n` +
							`**Modified:** \`${supervisorResult.modifiedCommand}\`\n` +
							`**Reason:** ${supervisorResult.reason}`
						)
						command = supervisorResult.modifiedCommand
					}

					// Get detailed interception analysis
					const interceptionResult = await interceptionEngine.interceptCommand(command, task.cwd)
					if (interceptionResult.action === "block") {
						// Show detailed refusal message with alternatives
						let refusalMessage = interceptionResult.reason

						if (interceptionResult.safeAlternatives.length > 0) {
							refusalMessage += `\n\n**💡 Safe Alternatives:**\n\`\`\`bash\n${interceptionResult.safeAlternatives.join('\n')}\n\`\`\``
						}

						if (interceptionResult.bypassCode) {
							refusalMessage += `\n\n**🔓 Emergency Bypass Code:** \`${interceptionResult.bypassCode}\`\n` +
								`*Use this code only if absolutely necessary and you understand the risks.*`
						}

						pushToolResult(formatResponse.toolError(refusalMessage))
						return
					}

					if (interceptionResult.action === "modify" && interceptionResult.modifiedCommand) {
						await task.say("text",
							`⚠️ **Command Modified for Safety**\n\n` +
							`**Original:** \`${command}\`\n` +
							`**Modified:** \`${interceptionResult.modifiedCommand}\`\n` +
							`**Reason:** ${interceptionResult.reason}`
						)
						command = interceptionResult.modifiedCommand
					}

				} catch (supervisorError) {
					// Log supervisor error but don't block execution
					console.warn("Supervisor error:", supervisorError)
					outputChannel.appendLine(`[Supervisor] Error: ${supervisorError}`)
				}
			}

			const didApprove = await askApproval("command", command)

			if (!didApprove) {
				return
			}

			const executionId = task.lastMessageTs?.toString() ?? Date.now().toString()
			// Reuse provider from earlier in the function
			const providerState = await provider?.getState()

			const {
				terminalOutputLineLimit = 500,
				terminalOutputCharacterLimit = DEFAULT_TERMINAL_OUTPUT_CHARACTER_LIMIT,
				terminalShellIntegrationDisabled = false,
			} = providerState ?? {}

			// Get command execution timeout from VSCode configuration (in seconds)
			const commandExecutionTimeoutSeconds = vscode.workspace
				.getConfiguration(Package.name)
				.get<number>("commandExecutionTimeout", 0)

			// Get command timeout allowlist from VSCode configuration
			const commandTimeoutAllowlist = vscode.workspace
				.getConfiguration(Package.name)
				.get<string[]>("commandTimeoutAllowlist", [])

			// Check if command matches any prefix in the allowlist
			const isCommandAllowlisted = commandTimeoutAllowlist.some((prefix) => command!.startsWith(prefix.trim()))

			// Convert seconds to milliseconds for internal use, but skip timeout if command is allowlisted
			const commandExecutionTimeout = isCommandAllowlisted ? 0 : commandExecutionTimeoutSeconds * 1000

			// Performance optimization: Check for fast-fail conditions before execution
			const perfCheck = task.checkOperationPerformance('execute_command', { command, cwd: customCwd })
			if (perfCheck.shouldFastFail) {
				await task.say("error",
					`⚡ **Fast-fail activated**\n\n` +
					`Command: \`${command}\`\n` +
					`Reason: ${perfCheck.reason}\n\n` +
					`This command was terminated immediately to save time and prevent wasted resources.`
				)
				pushToolResult(`Fast-fail: ${perfCheck.reason}`)
				return
			}

			// Apply performance optimizations
			if (perfCheck.optimizations.length > 0) {
				const highPriorityOpts = perfCheck.optimizations.filter(opt =>
					opt.priority === 'high' || opt.priority === 'critical'
				)
				if (highPriorityOpts.length > 0) {
					await task.say("text",
						`🚀 **Performance optimizations applied**\n\n` +
						highPriorityOpts.map(opt => `• ${opt.description}`).join('\n')
					)
				}
			}

			const options: ExecuteCommandOptions = {
				executionId,
				command,
				customCwd,
				terminalShellIntegrationDisabled,
				terminalOutputLineLimit,
				terminalOutputCharacterLimit,
				commandExecutionTimeout,
			}

			try {
				const [rejected, result] = await executeCommand(task, options)

				if (rejected) {
					task.didRejectTool = true
					// Record failed action
					task.recordAction(
						`execute_command: ${command}`,
						false,
						'Command was rejected',
						'execute_command',
						{ command, cwd: customCwd }
					)
				} else {
					// Record successful action
					task.recordAction(
						`execute_command: ${command}`,
						true,
						undefined,
						'execute_command',
						{ command, cwd: customCwd }
					)
				}

				pushToolResult(result)
			} catch (error: unknown) {
				const status: CommandExecutionStatus = { executionId, status: "fallback" }
				provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })
				await task.say("shell_integration_warning")

				if (error instanceof ShellIntegrationError) {
					const [rejected, result] = await executeCommand(task, {
						...options,
						terminalShellIntegrationDisabled: true,
					})

					if (rejected) {
						task.didRejectTool = true
						task.recordAction(
							`execute_command (fallback): ${command}`,
							false,
							'Command was rejected in fallback mode',
							'execute_command',
							{ command, cwd: customCwd, fallback: true }
						)
					} else {
						task.recordAction(
							`execute_command (fallback): ${command}`,
							true,
							undefined,
							'execute_command',
							{ command, cwd: customCwd, fallback: true }
						)
					}

					pushToolResult(result)
				} else {
					task.recordAction(
						`execute_command: ${command}`,
						false,
						`Shell integration error: ${error instanceof Error ? error.message : String(error)}`,
						'execute_command',
						{ command, cwd: customCwd }
					)
					pushToolResult(`Command failed to execute in terminal due to a shell integration error.`)
				}
			}

			return
		}
	} catch (error) {
		await handleError("executing command", error)
		return
	}
}

export type ExecuteCommandOptions = {
	executionId: string
	command: string
	customCwd?: string
	terminalShellIntegrationDisabled?: boolean
	terminalOutputLineLimit?: number
	terminalOutputCharacterLimit?: number
	commandExecutionTimeout?: number
}

export async function executeCommand(
	task: Task,
	{
		executionId,
		command,
		customCwd,
		terminalShellIntegrationDisabled = true, // kilocode_change: default
		terminalOutputLineLimit = 500,
		terminalOutputCharacterLimit = DEFAULT_TERMINAL_OUTPUT_CHARACTER_LIMIT,
		commandExecutionTimeout = 0,
	}: ExecuteCommandOptions,
): Promise<[boolean, ToolResponse]> {
	// Initialize dynamic timeout manager for intelligent monitoring
	let dynamicTimeoutManager: DynamicTimeoutManager | null = null
	let useDynamicTimeout = commandExecutionTimeout === 0
	const effectiveTimeout = commandExecutionTimeout

	if (useDynamicTimeout) {
		const timeoutConfig = DynamicTimeoutManager.getConfigForCommand(command)
		dynamicTimeoutManager = new DynamicTimeoutManager(timeoutConfig)

		await task.say("text",
			`🧠 **Intelligent command monitoring enabled**\n\n` +
			`Command: \`${command}\`\n\n` +
			`Using smart timeout detection that monitors output in real-time. ` +
			`Will terminate quickly if stuck or return immediately on success detection.`
		)
	}

	// Convert milliseconds back to seconds for display purposes.
	const commandExecutionTimeoutSeconds = effectiveTimeout / 1000
	let workingDir: string

	if (!customCwd) {
		workingDir = task.cwd
	} else if (path.isAbsolute(customCwd)) {
		workingDir = customCwd
	} else {
		workingDir = path.resolve(task.cwd, customCwd)
	}

	try {
		await fs.access(workingDir)
	} catch (error) {
		return [false, `Working directory '${workingDir}' does not exist.`]
	}

	let message: { text?: string; images?: string[] } | undefined
	let runInBackground = false
	let completed = false
	let result: string = ""
	let exitDetails: ExitCodeDetails | undefined
	let shellIntegrationError: string | undefined

	const terminalProvider = terminalShellIntegrationDisabled ? "execa" : "vscode"
	const provider = await task.providerRef.deref()

	let accumulatedOutput = ""
	const callbacks: RooTerminalCallbacks = {
		onLine: async (lines: string, process: RooTerminalProcess) => {
			accumulatedOutput += lines

			// Feed output to dynamic timeout manager for intelligent monitoring
			if (dynamicTimeoutManager) {
				dynamicTimeoutManager.processOutput(lines)
			}

			const compressedOutput = Terminal.compressTerminalOutput(
				accumulatedOutput,
				terminalOutputLineLimit,
				terminalOutputCharacterLimit,
			)
			const status: CommandExecutionStatus = { executionId, status: "output", output: compressedOutput }
			provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })

			if (runInBackground) {
				return
			}

			try {
				const { response, text, images } = await task.ask("command_output", "")
				runInBackground = true

				if (response === "messageResponse") {
					message = { text, images }
					process.continue()
				}
			} catch (_error) {}
		},
		onCompleted: (output: string | undefined) => {
			result = Terminal.compressTerminalOutput(
				output ?? "",
				terminalOutputLineLimit,
				terminalOutputCharacterLimit,
			)

			// Stop dynamic timeout monitoring
			if (dynamicTimeoutManager) {
				dynamicTimeoutManager.stop()
			}

			task.say("command_output", result)
			completed = true
		},
		onShellExecutionStarted: (pid: number | undefined) => {
			console.log(`[executeCommand] onShellExecutionStarted: ${pid}`)
			const status: CommandExecutionStatus = { executionId, status: "started", pid, command }
			provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })

			// Start dynamic timeout monitoring
			if (dynamicTimeoutManager) {
				dynamicTimeoutManager.start(command)
			}

			// Register process with supervisor for monitoring
			if (pid && provider?.getOutputChannel) {
				try {
					const supervisor = AutonomousSupervisor.getInstance(provider.getOutputChannel())
					supervisor.registerProcess(pid, command)

					// Store the supervisor instance and PID for cleanup
					task.supervisorPid = pid
					task.supervisorInstance = supervisor
				} catch (error) {
					console.warn("Failed to register process with supervisor:", error)
				}
			}
		},
		onShellExecutionComplete: (details: ExitCodeDetails) => {
			const status: CommandExecutionStatus = { executionId, status: "exited", exitCode: details.exitCode }
			provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })
			exitDetails = details

			// Unregister process from supervisor when it completes
			if (task.supervisorPid && task.supervisorInstance) {
				try {
					task.supervisorInstance.unregisterProcess(task.supervisorPid)
					task.supervisorPid = undefined
					task.supervisorInstance = undefined
				} catch (error) {
					console.warn("Failed to unregister process from supervisor:", error)
				}
			}
		},
	}

	if (terminalProvider === "vscode") {
		callbacks.onNoShellIntegration = async (error: string) => {
			TelemetryService.instance.captureShellIntegrationError(task.taskId)
			shellIntegrationError = error
		}
	}

	const terminal = await TerminalRegistry.getOrCreateTerminal(workingDir, !!customCwd, task.taskId, terminalProvider)

	if (terminal instanceof Terminal) {
		terminal.terminal.show(true)

		// Update the working directory in case the terminal we asked for has
		// a different working directory so that the model will know where the
		// command actually executed.
		workingDir = terminal.getCurrentWorkingDirectory()
	}

	// Initialize smart terminal integration for caching
	const smartTerminalIntegration = SmartTerminalIntegration.getInstance()
	await smartTerminalIntegration.initialize()

	// Try to use smart cache first, fallback to normal execution
	let process: any
	try {
		const cachedOutput = await smartTerminalIntegration.executeCommandWithSmartCache(terminal, command, callbacks)
		if (cachedOutput) {
			// Command was served from cache, create a mock process
			process = {
				command,
				continue: () => {},
				abort: () => {},
				hasUnretrievedOutput: () => false,
				getUnretrievedOutput: () => ""
			}
		} else {
			// Execute normally
			process = terminal.runCommand(command, callbacks)
		}
	} catch (error) {
		console.log("[SmartTerminalCache] Cache execution failed, falling back to normal execution:", error)
		process = terminal.runCommand(command, callbacks)
	}
	task.terminalProcess = process

	// Use dynamic timeout if enabled, otherwise use traditional timeout
	if (dynamicTimeoutManager) {
		let dynamicTimeoutPromise: Promise<never> | null = null

		dynamicTimeoutPromise = new Promise<never>((_, reject) => {
			dynamicTimeoutManager!.on('success', (reason: string, output: string, metrics: any) => {
				console.log(`[DynamicTimeout] Success detected: ${reason}`)
				reject(new Error('DYNAMIC_SUCCESS'))
			})

			dynamicTimeoutManager!.on('error', (reason: string, output: string, metrics: any) => {
				console.log(`[DynamicTimeout] Error detected: ${reason}`)
				task.terminalProcess?.abort()
				reject(new Error(`DYNAMIC_ERROR: ${reason}`))
			})

			dynamicTimeoutManager!.on('timeout', (reason: string, metrics: any) => {
				console.log(`[DynamicTimeout] Timeout: ${reason}`)
				task.terminalProcess?.abort()
				reject(new Error(`DYNAMIC_TIMEOUT: ${reason}`))
			})
		})

		try {
			await Promise.race([process, dynamicTimeoutPromise])
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error)

			if (errorMessage === 'DYNAMIC_SUCCESS') {
				// Command completed successfully via smart detection
				await task.say("text",
					`✅ **Command completed successfully!**\n\n` +
					`Smart monitoring detected completion early, saving time.`
				)
				return [
					true,
					`Command completed successfully. Smart monitoring detected completion early, saving time.`,
				]
			} else if (errorMessage.startsWith('DYNAMIC_ERROR:')) {
				const reason = errorMessage.replace('DYNAMIC_ERROR: ', '')
				await task.say("error",
					`❌ **Command failed**\n\n` +
					`Error detected: ${reason}\n\n` +
					`Smart monitoring terminated the command early to save time.`
				)
				return [
					false,
					`Command failed: ${reason}. Smart monitoring detected the error and terminated early.`,
				]
			} else if (errorMessage.startsWith('DYNAMIC_TIMEOUT:')) {
				const reason = errorMessage.replace('DYNAMIC_TIMEOUT: ', '')
				const status: CommandExecutionStatus = { executionId, status: "timeout" }
				provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })

				if (reason === 'inactivity_detected') {
					await task.say("error",
						`⏱️ **Command appears stuck**\n\n` +
						`No output detected for 30+ seconds. Command terminated to avoid wasting time.\n\n` +
						`This is much faster than waiting for a fixed timeout.`
					)
					return [
						false,
						`Command appeared stuck (no output for 30+ seconds) and was terminated early to save time.`,
					]
				} else {
					await task.say("error",
						`⏱️ **Command timeout**\n\n` +
						`Reason: ${reason}\n\n` +
						`Smart monitoring determined the command should be terminated.`
					)
					return [
						false,
						`Command timed out: ${reason}. Smart monitoring optimized the timeout duration.`,
					]
				}
			}
			throw error
		} finally {
			// Clean up dynamic timeout manager
			if (dynamicTimeoutManager) {
				dynamicTimeoutManager.stop()
				dynamicTimeoutManager.removeAllListeners()
			}

			// Clean up supervisor registration
			if (task.supervisorPid && task.supervisorInstance) {
				try {
					task.supervisorInstance.unregisterProcess(task.supervisorPid)
				} catch (error) {
					console.warn("Failed to unregister process from supervisor during cleanup:", error)
				}
				task.supervisorPid = undefined
				task.supervisorInstance = undefined
			}

			task.terminalProcess = undefined
		}
	} else {
		// Use traditional timeout or no timeout
		if (effectiveTimeout > 0) {
			let timeoutId: NodeJS.Timeout | undefined
			let isTimedOut = false

			const timeoutPromise = new Promise<void>((_, reject) => {
				timeoutId = setTimeout(() => {
					isTimedOut = true
					task.terminalProcess?.abort()
					reject(new Error(`Command execution timed out after ${effectiveTimeout}ms`))
				}, effectiveTimeout)
			})

			try {
				await Promise.race([process, timeoutPromise])
			} catch (error) {
				if (isTimedOut) {
					const status: CommandExecutionStatus = { executionId, status: "timeout" }
					provider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })
					await task.say("error", t("common:errors:command_timeout", { seconds: effectiveTimeout / 1000 }))
					return [
						false,
						`The command was terminated after exceeding a ${effectiveTimeout / 1000}s timeout.`,
					]
				}
				throw error
			} finally {
				if (timeoutId) {
					clearTimeout(timeoutId)
				}
			}
		} else {
			// No timeout - just wait for the process to complete
			await process
		}

		// Clean up supervisor registration
		if (task.supervisorPid && task.supervisorInstance) {
			try {
				task.supervisorInstance.unregisterProcess(task.supervisorPid)
			} catch (error) {
				console.warn("Failed to unregister process from supervisor during cleanup:", error)
			}
			task.supervisorPid = undefined
			task.supervisorInstance = undefined
		}

		task.terminalProcess = undefined
	}

	if (shellIntegrationError) {
		throw new ShellIntegrationError(shellIntegrationError)
	}

	// Wait for a short delay to ensure all messages are sent to the webview.
	// This delay allows time for non-awaited promises to be created and
	// for their associated messages to be sent to the webview, maintaining
	// the correct order of messages (although the webview is smart about
	// grouping command_output messages despite any gaps anyways).
	await delay(50)

	if (message) {
		const { text, images } = message
		await task.say("user_feedback", text, images)

		return [
			true,
			formatResponse.toolResult(
				[
					`Command is still running in terminal from '${terminal.getCurrentWorkingDirectory().toPosix()}'.`,
					result.length > 0 ? `Here's the output so far:\n${result}\n` : "\n",
					`The user provided the following feedback:`,
					`<feedback>\n${text}\n</feedback>`,
				].join("\n"),
				images,
			),
		]
	} else if (completed || exitDetails) {
		let exitStatus: string = ""

		if (exitDetails !== undefined) {
			if (exitDetails.signalName) {
				exitStatus = `Process terminated by signal ${exitDetails.signalName}`

				if (exitDetails.coreDumpPossible) {
					exitStatus += " - core dump possible"
				}
			} else if (exitDetails.exitCode === undefined) {
				result += "<VSCE exit code is undefined: terminal output and command execution status is unknown.>"
				exitStatus = `Exit code: <undefined, notify user>`
			} else {
				if (exitDetails.exitCode !== 0) {
					exitStatus += "Command execution was not successful, inspect the cause and adjust as needed.\n"
				}

				exitStatus += `Exit code: ${exitDetails.exitCode}`
			}
		} else {
			result += "<VSCE exitDetails == undefined: terminal output and command execution status is unknown.>"
			exitStatus = `Exit code: <undefined, notify user>`
		}

		let workingDirInfo = ` within working directory '${terminal.getCurrentWorkingDirectory().toPosix()}'`

		return [false, `Command executed in terminal ${workingDirInfo}. ${exitStatus}\nOutput:\n${result}`]
	} else {
		return [
			false,
			[
				`Command is still running in terminal ${workingDir ? ` from '${workingDir.toPosix()}'` : ""}.`,
				result.length > 0 ? `Here's the output so far:\n${result}\n` : "\n",
				"You will be updated on the terminal status and new output in the future.",
			].join("\n"),
		]
	}
}
