import { Task } from "../task/Task"
import { MultiSearchReplaceDiffStrategy } from "../diff/strategies/multi-search-replace"
import { MultiFileSearchReplaceDiffStrategy } from "../diff/strategies/multi-file-search-replace"

/**
 * Utility to optimize diff settings for better success rates
 */
export class DiffOptimizer {
	/**
	 * Auto-adjust fuzzy matching threshold based on recent failures
	 */
	static async optimizeThresholdForTask(cline: Task, filePath: string): Promise<number> {
		const currentThreshold = cline.fuzzyMatchThreshold || 0.95
		const failureCount = cline.consecutiveMistakeCountForApplyDiff.get(filePath) || 0
		
		// Gradually reduce threshold based on failure count
		let optimizedThreshold = currentThreshold
		
		if (failureCount >= 1) {
			optimizedThreshold = Math.max(0.85, currentThreshold - 0.05)
		}
		if (failureCount >= 2) {
			optimizedThreshold = Math.max(0.80, currentThreshold - 0.10)
		}
		if (failureCount >= 3) {
			optimizedThreshold = Math.max(0.75, currentThreshold - 0.15)
		}
		
		return optimizedThreshold
	}

	/**
	 * Create an optimized diff strategy with better defaults
	 */
	static createOptimizedStrategy(
		threshold?: number, 
		bufferLines?: number,
		useMultiFile: boolean = false
	): MultiSearchReplaceDiffStrategy | MultiFileSearchReplaceDiffStrategy {
		const optimizedThreshold = threshold || 0.95
		const optimizedBufferLines = bufferLines || 50 // Increased buffer for better matching
		
		if (useMultiFile) {
			return new MultiFileSearchReplaceDiffStrategy(optimizedThreshold, optimizedBufferLines)
		} else {
			return new MultiSearchReplaceDiffStrategy(optimizedThreshold, optimizedBufferLines)
		}
	}

	/**
	 * Suggest optimal settings based on file characteristics
	 */
	static analyzeFileAndSuggestSettings(fileContent: string): {
		suggestedThreshold: number
		suggestedBufferLines: number
		reasoning: string[]
	} {
		const lines = fileContent.split(/\r?\n/)
		const reasoning: string[] = []
		let suggestedThreshold = 0.95
		let suggestedBufferLines = 40

		// Analyze file characteristics
		const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length
		const hasComplexIndentation = lines.some(line => line.match(/^\s{8,}/)) // 8+ spaces
		const hasTabsAndSpaces = fileContent.includes('\t') && fileContent.includes('    ')
		const hasLongLines = lines.some(line => line.length > 120)
		const isLargeFile = lines.length > 500

		// Adjust based on complexity
		if (hasComplexIndentation) {
			suggestedThreshold = Math.max(0.90, suggestedThreshold - 0.05)
			reasoning.push("Complex indentation detected - lowered threshold for better matching")
		}

		if (hasTabsAndSpaces) {
			suggestedThreshold = Math.max(0.88, suggestedThreshold - 0.07)
			reasoning.push("Mixed tabs and spaces - lowered threshold for whitespace tolerance")
		}

		if (hasLongLines) {
			suggestedBufferLines = Math.max(60, suggestedBufferLines + 20)
			reasoning.push("Long lines detected - increased buffer for better context")
		}

		if (isLargeFile) {
			suggestedBufferLines = Math.max(80, suggestedBufferLines + 40)
			reasoning.push("Large file detected - increased buffer for better search range")
		}

		if (avgLineLength < 20) {
			suggestedThreshold = Math.min(0.98, suggestedThreshold + 0.03)
			reasoning.push("Short lines - increased threshold for precision")
		}

		return {
			suggestedThreshold,
			suggestedBufferLines,
			reasoning
		}
	}

	/**
	 * Generate user-friendly optimization recommendations
	 */
	static async generateOptimizationReport(
		currentThreshold: number,
		failureCount: number,
		fileContent?: string
	): Promise<string> {
		let report = "🔧 **Diff Optimization Report**\n\n"
		
		report += `**Current Settings:**\n`
		report += `- Fuzzy Match Threshold: ${Math.round(currentThreshold * 100)}%\n`
		report += `- Recent Failures: ${failureCount}\n\n`

		if (failureCount > 0) {
			const optimizedThreshold = await DiffOptimizer.optimizeThresholdForTask({
				fuzzyMatchThreshold: currentThreshold,
				consecutiveMistakeCountForApplyDiff: new Map([["current", failureCount]])
			} as any, "current")

			report += `**🎯 Recommended Adjustments:**\n`
			report += `- Lower threshold to ${Math.round(optimizedThreshold * 100)}% for better success rate\n`
			report += `- Increase buffer lines to 60-80 for larger search context\n\n`
		}

		if (fileContent) {
			const analysis = this.analyzeFileAndSuggestSettings(fileContent)
			report += `**📊 File Analysis:**\n`
			analysis.reasoning.forEach(reason => {
				report += `- ${reason}\n`
			})
			report += `\n**💡 Suggested Settings:**\n`
			report += `- Threshold: ${Math.round(analysis.suggestedThreshold * 100)}%\n`
			report += `- Buffer Lines: ${analysis.suggestedBufferLines}\n\n`
		}

		report += `**🚀 Quick Fixes:**\n`
		report += `1. Try threshold at 90-95% for most files\n`
		report += `2. Use 85-90% for files with complex formatting\n`
		report += `3. Use str_replace_editor as fallback for exact matching\n`
		report += `4. Break large changes into smaller, targeted diffs\n`

		return report
	}

	/**
	 * Test different thresholds to find optimal setting
	 */
	static async findOptimalThreshold(
		originalContent: string,
		searchContent: string,
		replaceContent: string
	): Promise<{
		optimalThreshold: number
		testResults: Array<{ threshold: number; success: boolean; score?: number }>
	}> {
		const testThresholds = [1.0, 0.98, 0.95, 0.92, 0.90, 0.88, 0.85]
		const testResults: Array<{ threshold: number; success: boolean; score?: number }> = []
		let optimalThreshold = 0.95

		for (const threshold of testThresholds) {
			try {
				const strategy = new MultiSearchReplaceDiffStrategy(threshold, 50)
				const diffContent = `test.ts
<<<<<<< SEARCH
${searchContent}
=======
${replaceContent}
>>>>>>> REPLACE`

				const result = await strategy.applyDiff(originalContent, diffContent)
				testResults.push({
					threshold,
					success: result.success,
					score: result.success ? 1.0 : 0.0
				})

				if (result.success && optimalThreshold === 0.95) {
					optimalThreshold = threshold
				}
			} catch (error) {
				testResults.push({
					threshold,
					success: false
				})
			}
		}

		return {
			optimalThreshold,
			testResults
		}
	}
}
