import { describe, it, expect, beforeEach, afterEach, vi } from "vitest"
import * as vscode from "vscode"
import { AutonomousSupervisor } from "../AutonomousSupervisor"
import { SupervisorConfigManager } from "../SupervisorConfigManager"

// Mock VSCode
vi.mock("vscode", () => ({
	workspace: {
		getConfiguration: vi.fn(() => ({
			get: vi.fn((key: string, defaultValue: any) => defaultValue)
		})),
		onDidChangeConfiguration: vi.fn(() => ({ dispose: vi.fn() }))
	},
	window: {
		createOutputChannel: vi.fn(() => ({
			appendLine: vi.fn(),
			dispose: vi.fn()
		}))
	}
}))

describe("AutonomousSupervisor", () => {
	let supervisor: AutonomousSupervisor
	let mockOutputChannel: any

	beforeEach(() => {
		mockOutputChannel = {
			appendLine: vi.fn(),
			dispose: vi.fn()
		}
		
		// Reset singleton
		;(AutonomousSupervisor as any).instance = null
		
		supervisor = AutonomousSupervisor.getInstance(mockOutputChannel)
	})

	afterEach(() => {
		supervisor.dispose()
	})

	describe("Command Monitoring", () => {
		it("should allow safe commands", async () => {
			const result = await supervisor.monitorCommand("ls -la")
			
			expect(result.allowed).toBe(true)
			expect(result.reason).toBeUndefined()
		})

		it("should block npm start commands", async () => {
			const result = await supervisor.monitorCommand("npm start")
			
			expect(result.allowed).toBe(false)
			expect(result.reason).toContain("unbounded process")
		})

		it("should block development server commands", async () => {
			const commands = [
				"yarn dev",
				"pnpm start",
				"webpack-dev-server",
				"vite",
				"next dev"
			]

			for (const command of commands) {
				const result = await supervisor.monitorCommand(command)
				expect(result.allowed).toBe(false)
				expect(result.reason).toBeDefined()
			}
		})

		it("should block long-running processes", async () => {
			const commands = [
				"tail -f logfile.txt",
				"watch ls",
				"while true; do echo hello; done",
				"docker-compose up"
			]

			for (const command of commands) {
				const result = await supervisor.monitorCommand(command)
				expect(result.allowed).toBe(false)
				expect(result.reason).toBeDefined()
			}
		})

		it("should allow commands in allowlist even if they match patterns", async () => {
			// Update config to allow npm start
			await supervisor.updateConfig({
				allowedLongRunningCommands: ["npm start"]
			})

			const result = await supervisor.monitorCommand("npm start")
			expect(result.allowed).toBe(true)
		})

		it("should modify commands when configured", async () => {
			const result = await supervisor.monitorCommand("npm start --production")
			
			if (result.modifiedCommand) {
				expect(result.modifiedCommand).not.toBe("npm start --production")
				expect(result.reason).toContain("modified")
			}
		})
	})

	describe("Message Monitoring", () => {
		it("should allow safe task messages", async () => {
			const message = {
				type: "newTask" as const,
				text: "Please help me write a test file"
			}

			const result = await supervisor.monitorMessage(message)
			expect(result).toEqual(message)
		})

		it("should modify risky task messages", async () => {
			const message = {
				type: "newTask" as const,
				text: "Start a development server for this project"
			}

			const result = await supervisor.monitorMessage(message)
			
			if (result && result.text !== message.text) {
				expect(result.text).toContain("time-limited")
			}
		})

		it("should block extremely risky task messages", async () => {
			const message = {
				type: "newTask" as const,
				text: "Run npm start and keep the server running indefinitely"
			}

			const result = await supervisor.monitorMessage(message)
			expect(result).toBeNull()
		})
	})

	describe("Process Registration", () => {
		it("should register and track processes", () => {
			const pid = 12345
			const command = "node server.js"

			supervisor.registerProcess(pid, command)
			
			const stats = supervisor.getStats()
			expect(stats.activeProcesses).toBe(1)
		})

		it("should unregister processes", () => {
			const pid = 12345
			const command = "node server.js"

			supervisor.registerProcess(pid, command)
			supervisor.unregisterProcess(pid)
			
			const stats = supervisor.getStats()
			expect(stats.activeProcesses).toBe(0)
		})

		it("should handle process timeouts", async () => {
			// Mock process.kill to avoid actual process termination
			const originalKill = process.kill
			process.kill = vi.fn()

			// Set very short timeout for testing
			supervisor.updateConfig({ maxProcessTimeout: 100 })

			const pid = 12345
			const command = "long-running-command"

			const emergencyKillPromise = new Promise<void>((resolve) => {
				supervisor.on("emergencyKill", (killedPid, reason) => {
					expect(killedPid).toBe(pid)
					expect(reason).toContain("SIGTERM")

					// Restore original process.kill
					process.kill = originalKill
					resolve()
				})
			})

			supervisor.registerProcess(pid, command)

			await emergencyKillPromise
		}, 1000)
	})

	describe("Configuration", () => {
		it("should load default configuration", () => {
			const stats = supervisor.getStats()
			expect(stats).toBeDefined()
		})

		it("should update configuration", async () => {
			const newConfig = {
				maxProcessTimeout: 60000,
				blockNpmStart: false
			}

			await supervisor.updateConfig(newConfig)
			
			// Configuration should be updated (tested indirectly through behavior)
			const result = await supervisor.monitorCommand("npm start")
			// Behavior should change based on blockNpmStart: false
		})

		it("should handle configuration changes", () => {
			const configManager = SupervisorConfigManager.getInstance()
			const newConfig = configManager.loadConfiguration()
			
			expect(newConfig).toBeDefined()
			expect(typeof newConfig.enabled).toBe("boolean")
			expect(typeof newConfig.maxProcessTimeout).toBe("number")
		})
	})

	describe("Statistics and Metrics", () => {
		it("should track decision statistics", async () => {
			await supervisor.monitorCommand("ls")
			await supervisor.monitorCommand("npm start")
			await supervisor.monitorCommand("git status")

			const stats = supervisor.getStats()
			expect(stats.decisionsCount).toBeGreaterThan(0)
			expect(stats.blockedCommands).toBeGreaterThan(0)
		})

		it("should provide performance metrics", () => {
			const metrics = supervisor.getPerformanceMetrics()
			
			expect(metrics).toBeDefined()
			expect(typeof metrics.sessionStartTime).toBe("number")
			expect(typeof metrics.totalDecisions).toBe("number")
			expect(typeof metrics.sessionUptime).toBe("number")
		})

		it("should provide security metrics", () => {
			const metrics = supervisor.getSecurityMetrics()
			
			expect(metrics).toBeDefined()
			expect(typeof metrics.highRiskCommandsBlocked).toBe("number")
			expect(typeof metrics.criticalRiskCommandsBlocked).toBe("number")
			expect(Array.isArray(metrics.topBlockedCommands)).toBe(true)
		})

		it("should generate summary report", () => {
			const report = supervisor.generateSummaryReport()
			
			expect(typeof report).toBe("string")
			expect(report).toContain("Session Summary")
			expect(report).toContain("Command Statistics")
		})

		it("should export logs", () => {
			const logs = supervisor.exportLogs()
			
			expect(typeof logs).toBe("string")
			
			const parsed = JSON.parse(logs)
			expect(parsed.sessionId).toBeDefined()
			expect(parsed.performanceMetrics).toBeDefined()
			expect(parsed.securityMetrics).toBeDefined()
		})
	})

	describe("Event Emission", () => {
		it("should emit processBlocked events", async () => {
			let eventEmitted = false
			
			supervisor.on("processBlocked", (command, reason) => {
				expect(command).toBe("npm start")
				expect(reason).toBeDefined()
				eventEmitted = true
			})

			await supervisor.monitorCommand("npm start")
			expect(eventEmitted).toBe(true)
		})

		it("should emit processModified events", async () => {
			let eventEmitted = false
			
			supervisor.on("processModified", (original, modified, reason) => {
				expect(original).toBeDefined()
				expect(modified).toBeDefined()
				expect(reason).toBeDefined()
				eventEmitted = true
			})

			// This might emit processModified depending on configuration
			await supervisor.monitorCommand("npm start --watch")
			
			// Event emission depends on specific configuration and command analysis
		})

		it("should emit decision events", async () => {
			let eventEmitted = false
			
			supervisor.on("decision", (decision) => {
				expect(decision.action).toBeDefined()
				expect(decision.reason).toBeDefined()
				expect(decision.timestamp).toBeDefined()
				eventEmitted = true
			})

			await supervisor.monitorCommand("ls")
			expect(eventEmitted).toBe(true)
		})
	})

	describe("Error Handling", () => {
		it("should handle invalid commands gracefully", async () => {
			const result = await supervisor.monitorCommand("")
			expect(result.allowed).toBe(true) // Empty commands are typically safe
		})

		it("should handle null/undefined inputs", async () => {
			const result = await supervisor.monitorCommand(null as any)
			expect(result).toBeDefined()
		})

		it("should continue working after errors", async () => {
			// Cause an error condition
			try {
				await supervisor.monitorCommand(null as any)
			} catch (error) {
				// Ignore error
			}

			// Should still work normally
			const result = await supervisor.monitorCommand("ls")
			expect(result.allowed).toBe(true)
		})
	})

	describe("Disabled State", () => {
		it("should allow all commands when disabled", async () => {
			await supervisor.updateConfig({ enabled: false })

			const result = await supervisor.monitorCommand("npm start")
			expect(result.allowed).toBe(true)
		})

		it("should not register processes when disabled", async () => {
			await supervisor.updateConfig({ enabled: false })

			supervisor.registerProcess(12345, "test-command")
			
			const stats = supervisor.getStats()
			expect(stats.activeProcesses).toBe(0)
		})
	})
})
