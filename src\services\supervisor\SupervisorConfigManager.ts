import * as vscode from "vscode"
import { Package } from "../../shared/package"
import type { SupervisorConfig } from "./AutonomousSupervisor"

/**
 * Configuration Manager for the Autonomous Supervisor
 * 
 * Handles loading, updating, and validating supervisor configuration
 * from VSCode settings with proper defaults and validation.
 */
export class SupervisorConfigManager {
	private static instance: SupervisorConfigManager | null = null
	private configChangeListener: vscode.Disposable | null = null
	private onConfigChangeCallbacks: Array<(config: SupervisorConfig) => void> = []

	private constructor() {
		// Listen for configuration changes
		this.configChangeListener = vscode.workspace.onDidChangeConfiguration((event) => {
			if (event.affectsConfiguration(Package.name)) {
				const newConfig = this.loadConfiguration()
				this.notifyConfigChange(newConfig)
			}
		})
	}

	public static getInstance(): SupervisorConfigManager {
		if (!SupervisorConfigManager.instance) {
			SupervisorConfigManager.instance = new SupervisorConfigManager()
		}
		return SupervisorConfigManager.instance
	}

	/**
	 * Load supervisor configuration from VSCode settings
	 */
	public loadConfiguration(): SupervisorConfig {
		const config = vscode.workspace.getConfiguration(Package.name)
		
		return {
			enabled: config.get("supervisor.enabled", true),
			maxProcessTimeout: this.validateTimeout(
				config.get("supervisor.maxProcessTimeout", 30000)
			) ?? 30000,
			unboundedProcessPatterns: this.validatePatterns(
				config.get("supervisor.unboundedProcessPatterns", this.getDefaultPatterns())
			) ?? this.getDefaultPatterns(),
			allowedLongRunningCommands: this.validateStringArray(
				config.get("supervisor.allowedLongRunningCommands", [])
			) ?? [],
			blockServerCommands: config.get("supervisor.blockServerCommands", true),
			blockNpmStart: config.get("supervisor.blockNpmStart", true),
			logDecisions: config.get("supervisor.logDecisions", true),
			emergencyKillTimeout: this.validateTimeout(
				config.get("supervisor.emergencyKillTimeout", 5000),
				1000,
				30000
			) ?? 5000
		}
	}

	/**
	 * Update supervisor configuration in VSCode settings
	 */
	public async updateConfiguration(updates: Partial<SupervisorConfig>): Promise<void> {
		const config = vscode.workspace.getConfiguration(Package.name)
		
		for (const [key, value] of Object.entries(updates)) {
			const settingKey = `supervisor.${key}`
			await config.update(settingKey, value, vscode.ConfigurationTarget.Global)
		}
	}

	/**
	 * Reset supervisor configuration to defaults
	 */
	public async resetToDefaults(): Promise<void> {
		const config = vscode.workspace.getConfiguration(Package.name)
		const defaultConfig = this.getDefaultConfiguration()
		
		for (const [key, value] of Object.entries(defaultConfig)) {
			const settingKey = `supervisor.${key}`
			await config.update(settingKey, value, vscode.ConfigurationTarget.Global)
		}
	}

	/**
	 * Add a callback to be notified when configuration changes
	 */
	public onConfigurationChange(callback: (config: SupervisorConfig) => void): vscode.Disposable {
		this.onConfigChangeCallbacks.push(callback)
		
		return {
			dispose: () => {
				const index = this.onConfigChangeCallbacks.indexOf(callback)
				if (index >= 0) {
					this.onConfigChangeCallbacks.splice(index, 1)
				}
			}
		}
	}

	/**
	 * Get configuration schema for validation
	 */
	public getConfigurationSchema(): Record<string, any> {
		return {
			enabled: { type: "boolean", default: true },
			maxProcessTimeout: { type: "number", minimum: 5000, maximum: 300000, default: 30000 },
			unboundedProcessPatterns: { type: "array", items: { type: "string" }, default: this.getDefaultPatterns() },
			allowedLongRunningCommands: { type: "array", items: { type: "string" }, default: [] },
			blockServerCommands: { type: "boolean", default: true },
			blockNpmStart: { type: "boolean", default: true },
			logDecisions: { type: "boolean", default: true },
			emergencyKillTimeout: { type: "number", minimum: 1000, maximum: 30000, default: 5000 }
		}
	}

	/**
	 * Validate and sanitize configuration values
	 */
	public validateConfiguration(config: Partial<SupervisorConfig>): SupervisorConfig {
		const defaults = this.getDefaultConfiguration()
		
		return {
			enabled: typeof config.enabled === "boolean" ? config.enabled : defaults.enabled,
			maxProcessTimeout: this.validateTimeout(config.maxProcessTimeout, 5000, 300000) || defaults.maxProcessTimeout,
			unboundedProcessPatterns: this.validatePatterns(config.unboundedProcessPatterns) || defaults.unboundedProcessPatterns,
			allowedLongRunningCommands: this.validateStringArray(config.allowedLongRunningCommands) || defaults.allowedLongRunningCommands,
			blockServerCommands: typeof config.blockServerCommands === "boolean" ? config.blockServerCommands : defaults.blockServerCommands,
			blockNpmStart: typeof config.blockNpmStart === "boolean" ? config.blockNpmStart : defaults.blockNpmStart,
			logDecisions: typeof config.logDecisions === "boolean" ? config.logDecisions : defaults.logDecisions,
			emergencyKillTimeout: this.validateTimeout(config.emergencyKillTimeout, 1000, 30000) || defaults.emergencyKillTimeout
		}
	}

	/**
	 * Export configuration for backup or sharing
	 */
	public exportConfiguration(): string {
		const config = this.loadConfiguration()
		return JSON.stringify(config, null, 2)
	}

	/**
	 * Import configuration from JSON string
	 */
	public async importConfiguration(configJson: string): Promise<void> {
		try {
			const importedConfig = JSON.parse(configJson)
			const validatedConfig = this.validateConfiguration(importedConfig)
			await this.updateConfiguration(validatedConfig)
		} catch (error) {
			throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : String(error)}`)
		}
	}

	private getDefaultConfiguration(): SupervisorConfig {
		return {
			enabled: true,
			maxProcessTimeout: 30000,
			unboundedProcessPatterns: this.getDefaultPatterns(),
			allowedLongRunningCommands: this.getDefaultAllowedCommands(),
			blockServerCommands: false, // Allow development servers
			blockNpmStart: false, // Allow npm start for development
			logDecisions: true,
			emergencyKillTimeout: 5000
		}
	}

	private getDefaultAllowedCommands(): string[] {
		return [
			"npm start",
			"npm run dev",
			"npm run serve",
			"yarn start",
			"yarn dev",
			"yarn serve",
			"pnpm start",
			"pnpm dev",
			"pnpm serve",
			"nodemon",
			"webpack-dev-server",
			"vite",
			"next dev",
			"nuxt dev",
			"gatsby develop",
			"hugo server",
			"jekyll serve",
			"python -m http.server",
			"python3 -m http.server",
			"http-server",
			"live-server",
			"serve",
			"tsc --watch",
			"webpack --watch",
			"sass --watch",
			"rollup --watch"
		]
	}

	private getDefaultPatterns(): string[] {
		return [
			// System-level dangerous operations
			"rm -rf /",
			"sudo rm",
			"format ",
			"fdisk",
			"mkfs",
			"dd if=",

			// Network security risks
			"nc.*-e",
			"netcat.*-e",
			"telnet.*23",
			"ftp.*anonymous",

			// Infinite loops and resource exhaustion
			"while true.*do.*done",
			"for.*;;.*do.*done",
			":(){ :|:& };:",
			"ping.*-t.*-l",

			// Unauthorized access attempts
			"ssh.*-o.*StrictHostKeyChecking=no",
			"curl.*-k.*https",
			"wget.*--no-check-certificate"
		]
	}

	private validateTimeout(value: any, min: number = 5000, max: number = 300000): number | null {
		if (typeof value !== "number" || isNaN(value)) {
			return null
		}
		return Math.max(min, Math.min(max, value))
	}

	private validatePatterns(value: any): string[] | null {
		if (!Array.isArray(value)) {
			return null
		}
		return value.filter(item => typeof item === "string" && item.trim().length > 0)
	}

	private validateStringArray(value: any): string[] | null {
		if (!Array.isArray(value)) {
			return null
		}
		return value.filter(item => typeof item === "string")
	}

	private notifyConfigChange(config: SupervisorConfig): void {
		for (const callback of this.onConfigChangeCallbacks) {
			try {
				callback(config)
			} catch (error) {
				console.error("Error in supervisor config change callback:", error)
			}
		}
	}

	/**
	 * Dispose of resources
	 */
	public dispose(): void {
		if (this.configChangeListener) {
			this.configChangeListener.dispose()
			this.configChangeListener = null
		}
		this.onConfigChangeCallbacks = []
		SupervisorConfigManager.instance = null
	}
}
