import * as vscode from "vscode"
import { AugmentContextEngine, ContextRetrievalOptions, ContextResult } from "./engine"
import { CommitLineageService, CommitLineageOptions, CommitSummary } from "./commit-lineage"
import { CommitIndexer, CommitIndexEntry } from "./commit-indexer"
import { AugmentEmbeddingService, EmbeddingOptions } from "./embedding-service"
import { getWorkspacePath } from "../../utils/path"

export interface EnhancedContextOptions extends ContextRetrievalOptions {
	includeFileHistory?: boolean
	includeRelatedCommits?: boolean
	prioritizeRecentChanges?: boolean
	crossRepoContext?: string[]
	embeddingOptions?: EmbeddingOptions
}

export interface EnhancedContextResult {
	codeContext: ContextResult[]
	commitContext: CommitSummary[]
	totalTokens: number
	retrievalTime: number
	sources: {
		codeIndex: number
		commitHistory: number
		fileHistory: number
	}
}

/**
 * Enhanced Context Manager that orchestrates all context retrieval capabilities
 * Similar to Augment Code's comprehensive context engine
 */
export class AugmentContextManager {
	private contextEngine: AugmentContextEngine
	private commitLineage: CommitLineageService
	private commitIndexer: CommitIndexer
	private embeddingService: AugmentEmbeddingService | undefined
	private workspacePath: string
	private isInitialized: boolean = false
	private validationCache: { valid: boolean; error?: string; timestamp: number } | null = null
	private readonly VALIDATION_CACHE_TTL = 30000 // 30 seconds

	constructor(embeddingOptions?: EmbeddingOptions) {
		this.workspacePath = getWorkspacePath()

		// Initialize embedding service if options provided
		if (embeddingOptions?.embeddingApiKey) {
			try {
				this.embeddingService = new AugmentEmbeddingService(embeddingOptions)
			} catch (error) {
				console.warn("Failed to initialize embedding service:", error)
			}
		}

		// Initialize context engine with embedding service
		this.contextEngine = new AugmentContextEngine(200_000, 50, this.embeddingService)
		this.commitLineage = new CommitLineageService()
		this.commitIndexer = new CommitIndexer({
			maxCommits: 1000,
			autoSummarize: true,
			watchInterval: 30000, // 30 seconds
		})
	}

	/**
	 * Initialize the context manager
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) {
			return
		}

		try {
			await this.contextEngine.initialize()
			await this.commitIndexer.initialize()
			this.isInitialized = true
		} catch (error) {
			console.error("Failed to initialize AugmentContextManager:", error)
			throw error
		}
	}

	/**
	 * Retrieve comprehensive context for a query
	 */
	async retrieveEnhancedContext(options: EnhancedContextOptions): Promise<EnhancedContextResult> {
		const startTime = Date.now()
		
		if (!this.isInitialized) {
			await this.initialize()
		}

		try {
			const {
				query,
				maxResults = 50,
				includeCommitHistory = true,
				includeFileHistory = false,
				includeRelatedCommits = true,
				prioritizeRecentChanges = true,
				directoryPrefix,
				contextWindowSize = 200_000,
			} = options

			// 1. Get code context from workspace indexing
			const codeContext = await this.contextEngine.retrieveContext({
				query,
				maxResults: Math.floor(maxResults * 0.4),
				includeCommitHistory: false, // We'll handle this separately
				directoryPrefix,
				contextWindowSize: Math.floor(contextWindowSize * 0.5),
			})

			// 2. Get semantic context using embeddings (if available and properly configured)
			console.log(`[AugmentContextManager] Embedding service available: ${!!this.embeddingService}`)
			let semanticContext: ContextResult[] = []
			if (this.embeddingService) {
				// Validate embedding service before using it
				const validationResult = await this.validateEmbeddingService()
				if (validationResult.valid) {
					semanticContext = await this.contextEngine.retrieveSemanticContext(query, Math.floor(maxResults * 0.3))
					console.log(`[AugmentContextManager] Retrieved ${semanticContext.length} semantic context results`)
				} else {
					console.warn(`[AugmentContextManager] Embedding service validation failed: ${validationResult.error}. Skipping semantic search.`)
				}
			}

			// 2. Get commit context
			let commitContext: CommitSummary[] = []
			
			if (includeCommitHistory) {
				// Search for commits related to the query
				const recentCommits = await this.commitLineage.searchCommitHistory(query, {
					maxCommits: Math.floor(maxResults * 0.2),
					summarizeWithLLM: true,
				})
				commitContext.push(...recentCommits)
			}

			if (includeRelatedCommits) {
				// Find commits that modified files in the code context
				const contextFiles = codeContext
					.filter(ctx => ctx.filePath)
					.map(ctx => ctx.filePath!)
					.slice(0, 5) // Limit to avoid too many queries

				const relatedCommits = await this.commitLineage.findRelatedCommits(query, contextFiles)
				commitContext.push(...relatedCommits)
			}

			if (includeFileHistory) {
				// Get file history for the most relevant files
				const topFiles = codeContext
					.filter(ctx => ctx.filePath)
					.slice(0, 3)
					.map(ctx => ctx.filePath!)

				for (const filePath of topFiles) {
					const fileHistory = await this.commitLineage.getFileHistory(filePath, 3)
					commitContext.push(...fileHistory)
				}
			}

			// Remove duplicate commits
			const uniqueCommits = new Map<string, CommitSummary>()
			for (const commit of commitContext) {
				uniqueCommits.set(commit.hash, commit)
			}
			commitContext = Array.from(uniqueCommits.values())

			// 3. Prioritize recent changes if requested
			if (prioritizeRecentChanges) {
				commitContext = this.prioritizeRecentCommits(commitContext)
				// Also boost recent code changes
				// This could be enhanced by checking file modification times
			}

			// 4. Combine and deduplicate code context (traditional + semantic)
			const combinedCodeContext = this.combineAndDeduplicateContext(codeContext, semanticContext)

			// 5. Calculate token usage and fit to context window
			const { finalCodeContext, finalCommitContext, totalTokens } = this.fitToContextWindow(
				combinedCodeContext,
				commitContext,
				contextWindowSize
			)

			const retrievalTime = Date.now() - startTime

			return {
				codeContext: finalCodeContext,
				commitContext: finalCommitContext,
				totalTokens,
				retrievalTime,
				sources: {
					codeIndex: finalCodeContext.length,
					commitHistory: finalCommitContext.filter(c => c.summary !== c.subject).length,
					fileHistory: finalCommitContext.filter(c => c.summary === c.subject).length,
				},
			}

		} catch (error) {
			console.error("Failed to retrieve enhanced context:", error)
			return {
				codeContext: [],
				commitContext: [],
				totalTokens: 0,
				retrievalTime: Date.now() - startTime,
				sources: { codeIndex: 0, commitHistory: 0, fileHistory: 0 },
			}
		}
	}

	/**
	 * Prioritize recent commits
	 */
	private prioritizeRecentCommits(commits: CommitSummary[]): CommitSummary[] {
		const now = Date.now()
		
		return commits.sort((a, b) => {
			const aDate = new Date(a.date).getTime()
			const bDate = new Date(b.date).getTime()
			
			// Weight recent commits higher
			const aDaysAgo = (now - aDate) / (1000 * 60 * 60 * 24)
			const bDaysAgo = (now - bDate) / (1000 * 60 * 60 * 24)
			
			const aWeight = Math.max(0, 1 - aDaysAgo / 30) // Decay over 30 days
			const bWeight = Math.max(0, 1 - bDaysAgo / 30)
			
			return bWeight - aWeight
		})
	}

	/**
	 * Fit context to the specified context window
	 */
	private fitToContextWindow(
		codeContext: ContextResult[],
		commitContext: CommitSummary[],
		contextWindowSize: number
	): {
		finalCodeContext: ContextResult[]
		finalCommitContext: CommitSummary[]
		totalTokens: number
	} {
		// Rough estimation: 4 characters per token
		const maxChars = contextWindowSize * 4
		let currentChars = 0

		const finalCodeContext: ContextResult[] = []
		const finalCommitContext: CommitSummary[] = []

		// Prioritize code context (70% of space)
		const codeSpaceLimit = maxChars * 0.7
		for (const context of codeContext) {
			const contextSize = context.content.length
			if (currentChars + contextSize <= codeSpaceLimit) {
				finalCodeContext.push(context)
				currentChars += contextSize
			} else {
				break
			}
		}

		// Add commit context with remaining space
		const remainingSpace = maxChars - currentChars
		for (const commit of commitContext) {
			const commitSize = commit.summary.length + commit.subject.length + 100 // metadata
			if (currentChars + commitSize <= maxChars) {
				finalCommitContext.push(commit)
				currentChars += commitSize
			} else {
				break
			}
		}

		return {
			finalCodeContext,
			finalCommitContext,
			totalTokens: Math.ceil(currentChars / 4),
		}
	}

	/**
	 * Get workspace indexing status
	 */
	getIndexingStatus() {
		return this.contextEngine.getIndexingStatus()
	}

	/**
	 * Clear all cached data
	 */
	async clearCache(): Promise<void> {
		await this.contextEngine.clearIndex()
		this.commitLineage.clearCache()
	}

	/**
	 * Refresh the context index
	 */
	async refreshIndex(): Promise<void> {
		if (!this.isInitialized) {
			await this.initialize()
		}

		// Clear existing index and reinitialize
		await this.contextEngine.clearIndex()
		await this.contextEngine.initialize()

		// Refresh commit indexing
		await this.commitIndexer.initialize()
	}

	/**
	 * Search for specific code patterns or implementations
	 */
	async searchCodePatterns(pattern: string, language?: string): Promise<ContextResult[]> {
		const query = language ? `${pattern} language:${language}` : pattern
		
		const results = await this.contextEngine.retrieveContext({
			query,
			maxResults: 20,
			includeCommitHistory: false,
		})

		return results.filter(result => result.type === "code")
	}

	/**
	 * Find historical context for "why was this introduced" queries
	 */
	async findIntroductionContext(query: string, filePath?: string): Promise<{
		introducingCommits: CommitSummary[]
		relatedCode: ContextResult[]
	}> {
		// Search for commits that might have introduced the functionality
		const introducingCommits = await this.commitLineage.searchCommitHistory(query, {
			maxCommits: 10,
			summarizeWithLLM: true,
		})

		// Get related code context
		const relatedCode = await this.contextEngine.retrieveContext({
			query,
			maxResults: 15,
			directoryPrefix: filePath ? filePath.split('/').slice(0, -1).join('/') : undefined,
			includeCommitHistory: false,
		})

		return {
			introducingCommits,
			relatedCode,
		}
	}

	/**
	 * Perform semantic search using embeddings
	 */
	async performSemanticSearch(
		query: string,
		codeChunks: string[],
		maxResults: number = 10
	): Promise<Array<{ content: string; score: number; index: number }>> {
		if (!this.embeddingService) {
			console.warn("Embedding service not available, skipping semantic search")
			return []
		}

		// Validate embedding service configuration before proceeding
		const validationResult = await this.validateEmbeddingService()
		if (!validationResult.valid) {
			console.warn(`Embedding service validation failed: ${validationResult.error}. Cannot perform semantic search.`)
			return []
		}

		try {
			// Create embedding for the query
			const queryEmbeddingResponse = await this.embeddingService.createEmbeddings([query])
			if (queryEmbeddingResponse.embeddings.length === 0) {
				return []
			}
			const queryEmbedding = queryEmbeddingResponse.embeddings[0]

			// Create embeddings for code chunks (in batches for efficiency)
			const chunkEmbeddingResponse = await this.embeddingService.createEmbeddings(codeChunks)
			const chunkEmbeddings = chunkEmbeddingResponse.embeddings

			// Calculate cosine similarity scores
			const results = chunkEmbeddings.map((chunkEmbedding, index) => ({
				content: codeChunks[index],
				score: this.calculateCosineSimilarity(queryEmbedding, chunkEmbedding),
				index,
			}))

			// Sort by score and return top results
			return results
				.sort((a, b) => b.score - a.score)
				.slice(0, maxResults)
				.filter(result => result.score > 0.1) // Filter out very low similarity scores

		} catch (error) {
			console.error("Semantic search failed:", error)
			return []
		}
	}

	/**
	 * Get embedding service status and information
	 */
	getEmbeddingServiceInfo() {
		if (!this.embeddingService) {
			return { available: false }
		}

		return {
			available: true,
			...this.embeddingService.getServiceInfo(),
		}
	}

	/**
	 * Validate embedding service configuration with caching
	 */
	async validateEmbeddingService() {
		if (!this.embeddingService) {
			return { valid: false, error: "Embedding service not configured" }
		}

		// Check cache first
		const now = Date.now()
		if (this.validationCache && (now - this.validationCache.timestamp) < this.VALIDATION_CACHE_TTL) {
			return { valid: this.validationCache.valid, error: this.validationCache.error }
		}

		// Perform validation and cache result
		const result = await this.embeddingService.validateConfiguration()
		this.validationCache = {
			valid: result.valid,
			error: result.error,
			timestamp: now
		}

		return result
	}

	/**
	 * Clear validation cache (call when embedding configuration changes)
	 */
	clearValidationCache() {
		this.validationCache = null
	}

	/**
	 * Update embedding service configuration
	 */
	updateEmbeddingService(embeddingOptions?: EmbeddingOptions) {
		this.clearValidationCache()

		if (embeddingOptions?.embeddingApiKey) {
			try {
				this.embeddingService = new AugmentEmbeddingService(embeddingOptions)
			} catch (error) {
				console.warn("Failed to update embedding service:", error)
				this.embeddingService = undefined
			}
		} else {
			this.embeddingService = undefined
		}
	}

	/**
	 * Calculate cosine similarity between two vectors
	 */
	private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
		if (vectorA.length !== vectorB.length) {
			throw new Error("Vectors must have the same length")
		}

		let dotProduct = 0
		let normA = 0
		let normB = 0

		for (let i = 0; i < vectorA.length; i++) {
			dotProduct += vectorA[i] * vectorB[i]
			normA += vectorA[i] * vectorA[i]
			normB += vectorB[i] * vectorB[i]
		}

		normA = Math.sqrt(normA)
		normB = Math.sqrt(normB)

		if (normA === 0 || normB === 0) {
			return 0
		}

		return dotProduct / (normA * normB)
	}

	/**
	 * Combine and deduplicate context from traditional and semantic search
	 */
	private combineAndDeduplicateContext(
		traditionalContext: ContextResult[],
		semanticContext: ContextResult[]
	): ContextResult[] {
		const combined = [...traditionalContext]
		const seenContent = new Set(traditionalContext.map(ctx => ctx.content))

		// Add semantic results that aren't already present
		for (const semanticResult of semanticContext) {
			if (!seenContent.has(semanticResult.content)) {
				combined.push({
					...semanticResult,
					metadata: {
						...semanticResult.metadata,
						source: "semantic",
					},
				})
				seenContent.add(semanticResult.content)
			}
		}

		// Sort by score (if available) and relevance
		return combined.sort((a, b) => {
			const scoreA = a.score || a.metadata?.semanticScore || 0
			const scoreB = b.score || b.metadata?.semanticScore || 0
			return scoreB - scoreA
		})
	}





	/**
	 * Dispose of resources
	 */
	dispose(): void {
		this.contextEngine.dispose()
	}
}
