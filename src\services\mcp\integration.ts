import * as vscode from "vscode"
import { MCPManager } from "./manager"
import { AugmentContextManager } from "../blue-ai-context/manager"
import {
	MCPServerConfig,
	MCPToolCall,
	MCPToolResult,
	MCPToolWithCategory,
	MCPToolCategory,
} from "./types"

export interface MCPIntegrationOptions {
	enableGitHub?: boolean
	enableFileSystem?: boolean
	enableWebSearch?: boolean
	githubToken?: string
	customServers?: Array<{
		name: string
		command: string
		args?: string[]
		env?: Record<string, string>
	}>
}

/**
 * MCP Integration Service
 * Bridges MCP tools with the Augment Context system
 */
export class MCPIntegrationService {
	private mcpManager: MCPManager
	private contextManager: AugmentContextManager
	private isInitialized: boolean = false

	constructor(contextManager?: AugmentContextManager) {
		this.mcpManager = new MCPManager()
		this.contextManager = contextManager || new AugmentContextManager()
	}

	/**
	 * Check if MCP integration is initialized
	 */
	get initialized(): boolean {
		return this.isInitialized
	}

	/**
	 * Initialize MCP integration
	 */
	async initialize(options: MCPIntegrationOptions = {}): Promise<void> {
		if (this.isInitialized) {
			return
		}

		try {
			// Build MCP server configuration
			const config: MCPServerConfig = {
				github: {
					enabled: options.enableGitHub ?? false,
					token: options.githubToken,
				},
				custom: options.customServers,
			}

			// Initialize MCP manager
			await this.mcpManager.initialize(config)

			// Set up event handlers
			this.setupEventHandlers()

			this.isInitialized = true
			console.log("MCP Integration Service initialized")

		} catch (error) {
			console.error("Failed to initialize MCP Integration Service:", error)
			throw error
		}
	}

	/**
	 * Set up event handlers for MCP events
	 */
	private setupEventHandlers(): void {
		this.mcpManager.on("server_connected", (serverName: string) => {
			console.log(`MCP server ${serverName} connected`)
			vscode.window.showInformationMessage(`Connected to ${serverName} integration`)
		})

		this.mcpManager.on("server_disconnected", (serverName: string) => {
			console.log(`MCP server ${serverName} disconnected`)
			vscode.window.showWarningMessage(`Disconnected from ${serverName} integration`)
		})

		this.mcpManager.on("server_error", (serverName: string, error: Error) => {
			console.error(`MCP server ${serverName} error:`, error)
			vscode.window.showErrorMessage(`Error with ${serverName} integration: ${error.message}`)
		})

		this.mcpManager.on("tool_called", (toolName: string, serverName: string, result: MCPToolResult) => {
			console.log(`Tool ${toolName} called on ${serverName}`)
		})
	}

	/**
	 * Execute a tool with context enhancement
	 */
	async executeToolWithContext(
		toolCall: MCPToolCall,
		enhanceWithWorkspaceContext: boolean = true
	): Promise<MCPToolResult & { contextEnhanced?: boolean }> {
		if (!this.isInitialized) {
			throw new Error("MCP Integration Service not initialized")
		}

		try {
			// Enhance tool arguments with workspace context if requested
			if (enhanceWithWorkspaceContext) {
				toolCall = await this.enhanceToolCallWithContext(toolCall)
			}

			// Execute the tool
			const result = await this.mcpManager.callTool(toolCall)

			return {
				...result,
				contextEnhanced: enhanceWithWorkspaceContext,
			}

		} catch (error) {
			console.error(`Failed to execute tool ${toolCall.name}:`, error)
			throw error
		}
	}

	/**
	 * Enhance tool call arguments with workspace context
	 */
	private async enhanceToolCallWithContext(toolCall: MCPToolCall): Promise<MCPToolCall> {
		try {
			// Get relevant context based on tool arguments
			const query = this.extractQueryFromToolCall(toolCall)
			
			if (!query) {
				return toolCall
			}

			// Retrieve context from the workspace
			const contextResult = await this.contextManager.retrieveEnhancedContext({
				query,
				maxResults: 10,
				includeCommitHistory: true,
				contextWindowSize: 50_000, // Smaller context for tool calls
			})

			// Enhance tool arguments with context
			const enhancedArguments = { ...toolCall.arguments }

			// Add context information to relevant arguments
			if (contextResult.codeContext.length > 0) {
				enhancedArguments._workspaceContext = {
					relevantFiles: contextResult.codeContext.map((ctx: any) => ({
						path: ctx.filePath,
						content: ctx.content.substring(0, 500), // Truncate for brevity
						language: ctx.metadata?.language,
					})),
				}
			}

			if (contextResult.commitContext.length > 0) {
				enhancedArguments._recentChanges = contextResult.commitContext.slice(0, 5).map((commit: any) => ({
					hash: commit.shortHash,
					subject: commit.subject,
					author: commit.author,
					date: commit.date,
					summary: commit.summary,
				}))
			}

			return {
				...toolCall,
				arguments: enhancedArguments,
			}

		} catch (error) {
			console.error("Failed to enhance tool call with context:", error)
			return toolCall
		}
	}

	/**
	 * Extract query from tool call arguments
	 */
	private extractQueryFromToolCall(toolCall: MCPToolCall): string | null {
		const args = toolCall.arguments

		// Common query fields
		const queryFields = ["query", "search", "message", "description", "title", "content", "text"]
		
		for (const field of queryFields) {
			if (args[field] && typeof args[field] === "string") {
				return args[field]
			}
		}

		// For GitHub tools, extract from issue/PR content
		if (toolCall.name.includes("github")) {
			if (args.body || args.title) {
				return `${args.title || ""} ${args.body || ""}`.trim()
			}
		}

		return null
	}

	/**
	 * Get available tools organized by category
	 */
	getAvailableTools(): Record<MCPToolCategory, MCPToolWithCategory[]> {
		const tools = this.mcpManager.getAllTools()
		const categorized: Record<MCPToolCategory, MCPToolWithCategory[]> = {} as any

		// Initialize categories
		for (const category of Object.values(MCPToolCategory)) {
			categorized[category] = []
		}

		// Categorize tools
		for (const tool of tools) {
			categorized[tool.category].push(tool)
		}

		return categorized
	}

	/**
	 * Get tools suitable for a specific context
	 */
	getToolsForContext(context: {
		language?: string
		fileType?: string
		projectType?: string
		query?: string
	}): MCPToolWithCategory[] {
		const allTools = this.mcpManager.getAllTools()
		const relevantTools: MCPToolWithCategory[] = []

		for (const tool of allTools) {
			// Filter tools based on context
			if (this.isToolRelevantForContext(tool, context)) {
				relevantTools.push(tool)
			}
		}

		return relevantTools
	}

	/**
	 * Check if a tool is relevant for the given context
	 */
	private isToolRelevantForContext(
		tool: MCPToolWithCategory,
		context: {
			language?: string
			fileType?: string
			projectType?: string
			query?: string
		}
	): boolean {
		// Always include file system tools
		if (tool.category === MCPToolCategory.FILE_SYSTEM) {
			return true
		}

		// Include version control tools for code-related contexts
		if (tool.category === MCPToolCategory.VERSION_CONTROL && 
			(context.language || context.fileType || context.projectType)) {
			return true
		}

		// Include search tools for queries
		if (tool.category === MCPToolCategory.SEARCH && context.query) {
			return true
		}

		// Include database tools for data-related projects
		if (tool.category === MCPToolCategory.DATABASE && 
			(context.projectType?.includes("database") || context.projectType?.includes("api"))) {
			return true
		}

		return false
	}

	/**
	 * Get integration statistics
	 */
	getStats() {
		return this.mcpManager.getStats()
	}

	/**
	 * Get server statuses
	 */
	getServerStatuses() {
		return this.mcpManager.getServerStatuses()
	}

	/**
	 * Suggest tools for a given query
	 */
	suggestToolsForQuery(query: string): MCPToolWithCategory[] {
		const allTools = this.mcpManager.getAllTools()
		const suggestions: Array<{ tool: MCPToolWithCategory; score: number }> = []

		const queryLower = query.toLowerCase()

		for (const tool of allTools) {
			let score = 0

			// Score based on tool name and description
			if (tool.name.toLowerCase().includes(queryLower)) {
				score += 10
			}
			if (tool.description.toLowerCase().includes(queryLower)) {
				score += 5
			}

			// Score based on query keywords
			if (queryLower.includes("github") && tool.category === MCPToolCategory.VERSION_CONTROL) {
				score += 8
			}
			if (queryLower.includes("file") && tool.category === MCPToolCategory.FILE_SYSTEM) {
				score += 8
			}
			if (queryLower.includes("search") && tool.category === MCPToolCategory.SEARCH) {
				score += 8
			}
			if (queryLower.includes("database") && tool.category === MCPToolCategory.DATABASE) {
				score += 8
			}

			if (score > 0) {
				suggestions.push({ tool, score })
			}
		}

		return suggestions
			.sort((a, b) => b.score - a.score)
			.slice(0, 10)
			.map(s => s.tool)
	}

	/**
	 * Dispose of resources
	 */
	dispose(): void {
		this.mcpManager.dispose()
	}
}
