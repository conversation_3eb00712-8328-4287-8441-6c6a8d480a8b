/**
 * Test script to validate supervisor changes
 * This script tests that the supervisor now allows legitimate development commands
 * while still blocking genuinely risky operations.
 */

import { AutonomousSupervisor } from './AutonomousSupervisor'
import * as vscode from 'vscode'

interface TestCase {
	command: string
	expectedAction: 'allow' | 'block' | 'modify'
	description: string
}

const testCases: TestCase[] = [
	// Development commands that should now be allowed
	{
		command: 'npm start',
		expectedAction: 'allow',
		description: 'npm start should be allowed for development'
	},
	{
		command: 'npm run dev',
		expectedAction: 'allow',
		description: 'npm run dev should be allowed for development'
	},
	{
		command: 'yarn start',
		expectedAction: 'allow',
		description: 'yarn start should be allowed for development'
	},
	{
		command: 'pnpm dev',
		expectedAction: 'allow',
		description: 'pnpm dev should be allowed for development'
	},
	{
		command: 'nodemon app.js',
		expectedAction: 'allow',
		description: 'nodemon should be allowed for development'
	},
	{
		command: 'webpack-dev-server',
		expectedAction: 'allow',
		description: 'webpack-dev-server should be allowed for development'
	},
	{
		command: 'vite',
		expectedAction: 'allow',
		description: 'vite should be allowed for development'
	},
	{
		command: 'next dev',
		expectedAction: 'allow',
		description: 'next dev should be allowed for development'
	},
	{
		command: 'python -m http.server',
		expectedAction: 'allow',
		description: 'python http server should be allowed for development'
	},
	{
		command: 'tsc --watch',
		expectedAction: 'allow',
		description: 'TypeScript watch mode should be allowed for development'
	},

	// Commands that should still be blocked (genuinely risky)
	{
		command: 'rm -rf /',
		expectedAction: 'block',
		description: 'rm -rf / should be blocked as dangerous'
	},
	{
		command: 'sudo rm -rf /home',
		expectedAction: 'block',
		description: 'sudo rm should be blocked as dangerous'
	},
	{
		command: 'format C:',
		expectedAction: 'block',
		description: 'format command should be blocked as dangerous'
	},
	{
		command: 'while true; do echo "infinite"; done',
		expectedAction: 'block',
		description: 'infinite loop should be blocked'
	},
	{
		command: 'ssh user@host -o StrictHostKeyChecking=no',
		expectedAction: 'block',
		description: 'insecure SSH should be blocked'
	},

	// Safe commands that should be allowed
	{
		command: 'npm install',
		expectedAction: 'allow',
		description: 'npm install should be allowed'
	},
	{
		command: 'npm test',
		expectedAction: 'allow',
		description: 'npm test should be allowed'
	},
	{
		command: 'git status',
		expectedAction: 'allow',
		description: 'git status should be allowed'
	},
	{
		command: 'ls -la',
		expectedAction: 'allow',
		description: 'ls command should be allowed'
	},
	{
		command: 'cat package.json',
		expectedAction: 'allow',
		description: 'cat command should be allowed'
	}
]

export async function testSupervisorChanges(): Promise<void> {
	console.log('🧪 Testing Supervisor Changes...\n')

	// Create a mock output channel for testing
	const mockOutputChannel: vscode.OutputChannel = {
		name: 'Test Supervisor',
		append: () => {},
		appendLine: () => {},
		replace: () => {},
		clear: () => {},
		show: () => {},
		hide: () => {},
		dispose: () => {}
	}

	const supervisor = AutonomousSupervisor.getInstance(mockOutputChannel)
	
	let passed = 0
	let failed = 0

	for (const testCase of testCases) {
		try {
			const result = await supervisor.monitorCommand(testCase.command)
			
			let actualAction: 'allow' | 'block' | 'modify'
			if (!result.allowed) {
				actualAction = 'block'
			} else if (result.modifiedCommand) {
				actualAction = 'modify'
			} else {
				actualAction = 'allow'
			}

			if (actualAction === testCase.expectedAction) {
				console.log(`✅ PASS: ${testCase.description}`)
				console.log(`   Command: "${testCase.command}" -> ${actualAction}`)
				passed++
			} else {
				console.log(`❌ FAIL: ${testCase.description}`)
				console.log(`   Command: "${testCase.command}"`)
				console.log(`   Expected: ${testCase.expectedAction}, Got: ${actualAction}`)
				if (result.reason) {
					console.log(`   Reason: ${result.reason}`)
				}
				failed++
			}
			console.log('')
		} catch (error) {
			console.log(`💥 ERROR: ${testCase.description}`)
			console.log(`   Command: "${testCase.command}"`)
			console.log(`   Error: ${error}`)
			failed++
			console.log('')
		}
	}

	console.log(`\n📊 Test Results:`)
	console.log(`   ✅ Passed: ${passed}`)
	console.log(`   ❌ Failed: ${failed}`)
	console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`)

	if (failed === 0) {
		console.log('\n🎉 All tests passed! The supervisor changes are working correctly.')
	} else {
		console.log('\n⚠️  Some tests failed. Please review the supervisor configuration.')
	}
}

// Export for use in other test files
export { testCases }
export type { TestCase }
