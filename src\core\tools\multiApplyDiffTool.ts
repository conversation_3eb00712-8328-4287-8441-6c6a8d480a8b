import path from "path"
import fs from "fs/promises"

import { TelemetryService } from "@blue-ai-coder/telemetry"
import { DEFAULT_WRITE_DELAY_MS } from "@blue-ai-coder/types"

import { ClineSayTool } from "../../shared/ExtensionMessage"
import { getReadablePath } from "../../utils/path"
import { Task } from "../task/Task"
import { ToolUse, RemoveClosingTag, AskApproval, HandleError, PushToolResult } from "../../shared/tools"
import { formatResponse } from "../prompts/responses"
import { fileExistsAtPath } from "../../utils/fs"
import { RecordSource } from "../context-tracking/FileContextTrackerTypes"
import { unescapeHtmlEntities } from "../../utils/text-normalization"
import { parseXml } from "../../utils/xml"
import { EXPERIMENT_IDS, experiments } from "../../shared/experiments"
import { applyDiffToolLegacy } from "./applyDiffTool"

interface DiffOperation {
	path: string
	diff: Array<{
		content: string
		startLine?: number
	}>
}

/**
 * Generate intelligent fallback suggestions for apply_diff parameter validation errors
 */
function generateFallbackSuggestions(args: string, errorMessage: string): string {
	const suggestions: string[] = []

	// Check for common XML issues
	if (!args.includes('<args>')) {
		suggestions.push("• **Missing <args> wrapper**: Wrap your content in <args>...</args> tags")
	}

	if (!args.includes('<file>')) {
		suggestions.push("• **Missing <file> tags**: Each file must be wrapped in <file>...</file> tags")
	}

	if (!args.includes('<path>')) {
		suggestions.push("• **Missing <path> tags**: Each file must have a <path>relative/path/to/file</path> element")
	}

	if (!args.includes('<diff>')) {
		suggestions.push("• **Missing <diff> tags**: Each file must have at least one <diff>...</diff> element")
	}

	if (!args.includes('<content>')) {
		suggestions.push("• **Missing <content> tags**: Each diff must have <content>...</content> with the actual diff")
	}

	// Check for SEARCH/REPLACE pattern
	if (!args.includes('<<<<<<< SEARCH') && !args.includes('=======') && !args.includes('>>>>>>> REPLACE')) {
		suggestions.push("• **Missing SEARCH/REPLACE pattern**: Use the standard diff format with <<<<<<< SEARCH, =======, >>>>>>> REPLACE markers")
	}

	// Check for unclosed tags
	const openTags = (args.match(/<[^/][^>]*>/g) || []).length
	const closeTags = (args.match(/<\/[^>]*>/g) || []).length
	if (openTags !== closeTags) {
		suggestions.push(`• **Unclosed XML tags**: Found ${openTags} opening tags but ${closeTags} closing tags - ensure all tags are properly closed`)
	}

	// Check for empty content
	if (args.includes('<content></content>') || args.includes('<content/>')) {
		suggestions.push("• **Empty content tags**: <content> tags must contain actual diff content, not be empty")
	}

	// Check for invalid characters
	if (args.includes('&') && !args.includes('&amp;') && !args.includes('&lt;') && !args.includes('&gt;')) {
		suggestions.push("• **Unescaped XML characters**: Escape special characters: & → &amp;, < → &lt;, > → &gt;")
	}

	if (suggestions.length === 0) {
		suggestions.push("• **Try simplifying**: Start with a single file and single diff to test the structure")
		suggestions.push("• **Check encoding**: Ensure the XML uses UTF-8 encoding without special characters")
	}

	return suggestions.length > 0
		? `**Specific Suggestions**:\n${suggestions.join('\n')}`
		: "**Suggestion**: Try using the legacy format with separate 'path' and 'diff' parameters as a fallback."
}

// Track operation status
interface OperationResult {
	path: string
	status: "pending" | "approved" | "denied" | "blocked" | "error"
	error?: string
	result?: string
	diffItems?: Array<{ content: string; startLine?: number }>
	absolutePath?: string
	fileExists?: boolean
}

// Add proper type definitions
interface ParsedFile {
	path: string
	diff: ParsedDiff | ParsedDiff[]
}

interface ParsedDiff {
	content: string
	start_line?: string
}

interface ParsedXmlResult {
	file: ParsedFile | ParsedFile[]
}

export async function applyDiffTool(
	cline: Task,
	block: ToolUse,
	askApproval: AskApproval,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	removeClosingTag: RemoveClosingTag,
) {
	// Check if MULTI_FILE_APPLY_DIFF experiment is enabled
	const provider = cline.providerRef.deref()
	if (provider) {
		const state = await provider.getState()
		const isMultiFileApplyDiffEnabled = experiments.isEnabled(
			state.experiments ?? {},
			EXPERIMENT_IDS.MULTI_FILE_APPLY_DIFF,
		)

		// If experiment is disabled, use legacy tool
		if (!isMultiFileApplyDiffEnabled) {
			return applyDiffToolLegacy(cline, block, askApproval, handleError, pushToolResult, removeClosingTag)
		}
	}

	// Otherwise, continue with new multi-file implementation
	const argsXmlTag: string | undefined = block.params.args
	const legacyPath: string | undefined = block.params.path
	const legacyDiffContent: string | undefined = block.params.diff
	const legacyStartLineStr: string | undefined = block.params.start_line

	let operationsMap: Record<string, DiffOperation> = {}
	let usingLegacyParams = false
	let filteredOperationErrors: string[] = []

	// Handle partial message first
	if (block.partial) {
		let filePath = ""
		if (argsXmlTag) {
			const match = argsXmlTag.match(/<file>.*?<path>([^<]+)<\/path>/s)
			if (match) {
				filePath = match[1]
			}
		} else if (legacyPath) {
			// Use legacy path if argsXmlTag is not present for partial messages
			filePath = legacyPath
		}

		const sharedMessageProps: ClineSayTool = {
			tool: "appliedDiff",
			path: getReadablePath(cline.cwd, filePath),
		}
		const partialMessage = JSON.stringify(sharedMessageProps)
		await cline.ask("tool", partialMessage, block.partial).catch(() => {})
		return
	}

	if (argsXmlTag) {
		// Parse file entries from XML (new way) with comprehensive validation
		try {
			// Validate args parameter is not empty or just whitespace
			if (!argsXmlTag.trim()) {
				throw new Error("args parameter is empty or contains only whitespace")
			}

			// Validate basic XML structure before parsing
			if (!argsXmlTag.includes('<file>') || !argsXmlTag.includes('<path>') || !argsXmlTag.includes('<diff>')) {
				throw new Error("args parameter missing required XML elements: <file>, <path>, or <diff>")
			}

			const parsed = parseXml(argsXmlTag, ["file.diff.content"]) as ParsedXmlResult

			// Validate parsed XML structure
			if (!parsed) {
				throw new Error("Parsed XML is null or undefined")
			}

			const files = Array.isArray(parsed.file) ? parsed.file : [parsed.file].filter(Boolean)

			if (files.length === 0) {
				throw new Error("No valid <file> elements found in args parameter")
			}

			for (const file of files) {
				// Enhanced validation for file structure
				if (!file) {
					console.warn("Skipping null/undefined file element")
					continue
				}

				if (!file.path || typeof file.path !== 'string') {
					throw new Error("File element missing valid <path> tag")
				}

				const filePath = file.path.trim()
				if (!filePath) {
					throw new Error("File path is empty after trimming whitespace")
				}

				if (!file.diff) {
					throw new Error(`File "${filePath}" missing <diff> element`)
				}

				// Initialize the operation in the map if it doesn't exist
				if (!operationsMap[filePath]) {
					operationsMap[filePath] = {
						path: filePath,
						diff: [],
					}
				}

				// Handle diff as either array or single element
				const diffs = Array.isArray(file.diff) ? file.diff : [file.diff]

				if (diffs.length === 0) {
					throw new Error(`File "${filePath}" has no valid <diff> elements`)
				}

				for (let i = 0; i < diffs.length; i++) {
					const diff = diffs[i]

					if (!diff) {
						console.warn(`Skipping null/undefined diff for file: ${filePath}`)
						continue
					}

					if (!diff.content || typeof diff.content !== 'string') {
						throw new Error(`File "${filePath}" has diff element missing valid <content> tag`)
					}

					const diffContent = diff.content.trim()
					if (!diffContent) {
						throw new Error(`File "${filePath}" has empty diff content`)
					}

					const startLine: number | undefined = diff.start_line ? parseInt(diff.start_line) : undefined

					operationsMap[filePath].diff.push({
						content: diffContent,
						startLine,
					})
				}

				// Validate that we have at least one valid diff for this file
				if (operationsMap[filePath].diff.length === 0) {
					throw new Error(`File "${filePath}" has no valid diff content after validation`)
				}
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : String(error)

			// Record the action for progress monitoring
			cline.recordAction(
				`apply_diff: parse XML args`,
				false,
				errorMessage,
				'apply_diff',
				{ argsLength: argsXmlTag.length, hasXMLStructure: argsXmlTag.includes('<file>') }
			)

			// Try to provide intelligent fallback suggestions
			const fallbackSuggestions = generateFallbackSuggestions(argsXmlTag, errorMessage)

			const detailedError = `❌ **Failed to parse apply_diff XML**

**Error**: ${errorMessage}

**Common Issues & Solutions**:
1. **Malformed XML**: Ensure all tags are properly closed and nested
2. **Missing required tags**: Must include <file>, <path>, and <diff> elements
3. **Empty content**: All <content> tags must have actual diff content
4. **Invalid characters**: Check for unescaped XML characters like <, >, &

**Expected structure**:
\`\`\`xml
<args>
  <file>
    <path>relative/path/to/file.ext</path>
    <diff>
      <content>
<<<<<<< SEARCH
old content here
=======
new content here
>>>>>>> REPLACE
      </content>
      <start_line>optional line number</start_line>
    </diff>
  </file>
</args>
\`\`\`

${fallbackSuggestions}

**Validation Details**: ${errorMessage}`

			cline.consecutiveMistakeCount++
			cline.recordToolError("apply_diff", errorMessage)
			TelemetryService.instance.captureDiffApplicationError(cline.taskId, cline.consecutiveMistakeCount)
			await cline.say("diff_error", `Failed to parse apply_diff XML: ${errorMessage}`)
			pushToolResult(detailedError)
			return
		}
	} else if (legacyPath && typeof legacyDiffContent === "string") {
		// Handle legacy parameters (old way)
		usingLegacyParams = true
		operationsMap[legacyPath] = {
			path: legacyPath,
			diff: [
				{
					content: legacyDiffContent, // Unescaping will be handled later like new diffs
					startLine: legacyStartLineStr ? parseInt(legacyStartLineStr) : undefined,
				},
			],
		}
	} else {
		// Neither new XML args nor old path/diff params are sufficient
		cline.consecutiveMistakeCount++
		cline.recordToolError("apply_diff")
		const errorMsg = await cline.sayAndCreateMissingParamError(
			"apply_diff",
			"args (or legacy 'path' and 'diff' parameters)",
		)
		pushToolResult(errorMsg)
		return
	}

	// If no operations were extracted, bail out with detailed error
	if (Object.keys(operationsMap).length === 0) {
		cline.consecutiveMistakeCount++
		cline.recordToolError("apply_diff", "No valid operations found")

		// Record the action for progress monitoring
		cline.recordAction(
			`apply_diff: validate operations`,
			false,
			'No valid file operations found after parsing',
			'apply_diff',
			{ usingLegacyParams, argsProvided: !!argsXmlTag, pathProvided: !!legacyPath }
		)

		const detailedError = usingLegacyParams
			? `❌ **No valid legacy parameters found**

**Issue**: The legacy 'path' and 'diff' parameters are missing or invalid.

**Required for legacy format**:
- \`path\`: A valid relative file path (e.g., "src/file.js")
- \`diff\`: Non-empty diff content with SEARCH/REPLACE blocks

**Example legacy usage**:
\`\`\`
path: "src/example.js"
diff: "<<<<<<< SEARCH\\nold code\\n=======\\nnew code\\n>>>>>>> REPLACE"
\`\`\`

**Suggestion**: Use the new XML format with <args> parameter for better reliability.`
			: `❌ **No valid file operations found**

**Issue**: The args parameter was provided but no valid file operations could be extracted.

**This usually means**:
1. All <file> elements were malformed or missing required fields
2. All <path> elements were empty or invalid
3. All <diff> elements were missing or had empty content
4. XML parsing succeeded but validation failed

**Next steps**:
1. Verify each <file> has a valid <path> and at least one <diff>
2. Ensure all <content> tags have actual diff content
3. Check that file paths are relative and valid
4. Try with a single file first to test the structure`

		await cline.say("error", detailedError)
		pushToolResult(detailedError)
		return
	}

	// Convert map to array of operations for processing
	const operations = Object.values(operationsMap)

	const operationResults: OperationResult[] = operations.map((op) => ({
		path: op.path,
		status: "pending",
		diffItems: op.diff,
	}))

	// Function to update operation result
	const updateOperationResult = (path: string, updates: Partial<OperationResult>) => {
		const index = operationResults.findIndex((result) => result.path === path)
		if (index !== -1) {
			operationResults[index] = { ...operationResults[index], ...updates }
		}
	}

	try {
		// First validate all files and prepare for batch approval
		const operationsToApprove: OperationResult[] = []
		const allDiffErrors: string[] = [] // Collect all diff errors

		for (const operation of operations) {
			const { path: relPath, diff: diffItems } = operation

			// Verify file access is allowed
			const accessAllowed = cline.rooIgnoreController?.validateAccess(relPath)
			if (!accessAllowed) {
				await cline.say("rooignore_error", relPath)
				updateOperationResult(relPath, {
					status: "blocked",
					error: formatResponse.rooIgnoreError(relPath),
				})
				continue
			}

			// Check if file is write-protected
			const isWriteProtected = cline.rooProtectedController?.isWriteProtected(relPath) || false

			// Verify file exists
			const absolutePath = path.resolve(cline.cwd, relPath)
			const fileExists = await fileExistsAtPath(absolutePath)
			if (!fileExists) {
				updateOperationResult(relPath, {
					status: "blocked",
					error: `File does not exist at path: ${absolutePath}`,
				})
				continue
			}

			// Add to operations that need approval
			const opResult = operationResults.find((r) => r.path === relPath)
			if (opResult) {
				opResult.absolutePath = absolutePath
				opResult.fileExists = fileExists
				operationsToApprove.push(opResult)
			}
		}

		// Handle batch approval if there are multiple files
		if (operationsToApprove.length > 1) {
			// Check if any files are write-protected
			const hasProtectedFiles = operationsToApprove.some(
				(opResult) => cline.rooProtectedController?.isWriteProtected(opResult.path) || false,
			)

			// Prepare batch diff data
			const batchDiffs = operationsToApprove.map((opResult) => {
				const readablePath = getReadablePath(cline.cwd, opResult.path)
				const changeCount = opResult.diffItems?.length || 0
				const changeText = changeCount === 1 ? "1 change" : `${changeCount} changes`

				return {
					path: readablePath,
					changeCount,
					key: `${readablePath} (${changeText})`,
					content: opResult.path, // Full relative path
					diffs: opResult.diffItems?.map((item) => ({
						content: item.content,
						startLine: item.startLine,
					})),
				}
			})

			const completeMessage = JSON.stringify({
				tool: "appliedDiff",
				batchDiffs,
				isProtected: hasProtectedFiles,
			} satisfies ClineSayTool)

			const { response, text, images } = await cline.ask("tool", completeMessage, hasProtectedFiles)

			// Process batch response
			if (response === "yesButtonClicked") {
				// Approve all files
				if (text) {
					await cline.say("user_feedback", text, images)
				}
				operationsToApprove.forEach((opResult) => {
					updateOperationResult(opResult.path, { status: "approved" })
				})
			} else if (response === "noButtonClicked") {
				// Deny all files
				if (text) {
					await cline.say("user_feedback", text, images)
				}
				cline.didRejectTool = true
				operationsToApprove.forEach((opResult) => {
					updateOperationResult(opResult.path, {
						status: "denied",
						result: `Changes to ${opResult.path} were not approved by user`,
					})
				})
			} else {
				// Handle individual permissions from objectResponse
				try {
					const parsedResponse = JSON.parse(text || "{}")
					// Check if this is our batch diff approval response
					if (parsedResponse.action === "applyDiff" && parsedResponse.approvedFiles) {
						const approvedFiles = parsedResponse.approvedFiles
						let hasAnyDenial = false

						operationsToApprove.forEach((opResult) => {
							const approved = approvedFiles[opResult.path] === true

							if (approved) {
								updateOperationResult(opResult.path, { status: "approved" })
							} else {
								hasAnyDenial = true
								updateOperationResult(opResult.path, {
									status: "denied",
									result: `Changes to ${opResult.path} were not approved by user`,
								})
							}
						})

						if (hasAnyDenial) {
							cline.didRejectTool = true
						}
					} else {
						// Legacy individual permissions format
						const individualPermissions = parsedResponse
						let hasAnyDenial = false

						batchDiffs.forEach((batchDiff, index) => {
							const opResult = operationsToApprove[index]
							const approved = individualPermissions[batchDiff.key] === true

							if (approved) {
								updateOperationResult(opResult.path, { status: "approved" })
							} else {
								hasAnyDenial = true
								updateOperationResult(opResult.path, {
									status: "denied",
									result: `Changes to ${opResult.path} were not approved by user`,
								})
							}
						})

						if (hasAnyDenial) {
							cline.didRejectTool = true
						}
					}
				} catch (error) {
					// Fallback: if JSON parsing fails, deny all files
					console.error("Failed to parse individual permissions:", error)
					cline.didRejectTool = true
					operationsToApprove.forEach((opResult) => {
						updateOperationResult(opResult.path, {
							status: "denied",
							result: `Changes to ${opResult.path} were not approved by user`,
						})
					})
				}
			}
		} else if (operationsToApprove.length === 1) {
			// Single file approval - process immediately
			const opResult = operationsToApprove[0]
			updateOperationResult(opResult.path, { status: "approved" })
		}

		// Process approved operations
		const results: string[] = []

		for (const opResult of operationResults) {
			// Skip operations that weren't approved or were blocked
			if (opResult.status !== "approved") {
				if (opResult.result) {
					results.push(opResult.result)
				} else if (opResult.error) {
					results.push(opResult.error)
				}
				continue
			}

			const relPath = opResult.path
			const diffItems = opResult.diffItems || []
			const absolutePath = opResult.absolutePath!
			const fileExists = opResult.fileExists!

			try {
				let originalContent: string | null = await fs.readFile(absolutePath, "utf-8")
				let successCount = 0
				let formattedError = ""

				// Pre-process all diff items for HTML entity unescaping if needed
				const processedDiffItems = !cline.api.getModel().id.includes("claude")
					? diffItems.map((item) => ({
							...item,
							content: item.content ? unescapeHtmlEntities(item.content) : item.content,
						}))
					: diffItems

				// Apply all diffs at once with the array-based method
				const diffResult = (await cline.diffStrategy?.applyDiff(originalContent, processedDiffItems)) ?? {
					success: false,
					error: "No diff strategy available - please ensure a valid diff strategy is configured",
				}

				// Release the original content from memory as it's no longer needed
				originalContent = null

				if (!diffResult.success) {
					cline.consecutiveMistakeCount++
					const currentCount = (cline.consecutiveMistakeCountForApplyDiff.get(relPath) || 0) + 1
					cline.consecutiveMistakeCountForApplyDiff.set(relPath, currentCount)

					TelemetryService.instance.captureDiffApplicationError(cline.taskId, currentCount)

					if (diffResult.failParts && diffResult.failParts.length > 0) {
						for (let i = 0; i < diffResult.failParts.length; i++) {
							const failPart = diffResult.failParts[i]
							if (failPart.success) {
								continue
							}

							// Collect error for later reporting
							allDiffErrors.push(`${relPath} - Diff ${i + 1}: ${failPart.error}`)

							const errorDetails = failPart.details ? JSON.stringify(failPart.details, null, 2) : ""
							formattedError += `<error_details>
Diff ${i + 1} failed for file: ${relPath}
Error: ${failPart.error}

Suggested fixes:
1. Verify the search content exactly matches the file content (including whitespace and case)
2. Check for correct indentation and line endings
3. Use <read_file> to see the current file content
4. Consider breaking complex changes into smaller diffs
5. Ensure start_line parameter matches the actual content location
${errorDetails ? `\nDetailed error information:\n${errorDetails}\n` : ""}
</error_details>\n\n`
						}
					} else {
						const errorDetails = diffResult.details ? JSON.stringify(diffResult.details, null, 2) : ""
						formattedError += `<error_details>
Unable to apply diffs to file: ${absolutePath}
Error: ${diffResult.error}

Recovery suggestions:
1. Use <read_file> to examine the current file content
2. Verify the diff format matches the expected search/replace pattern
3. Check that the search content exactly matches what's in the file
4. Consider using line numbers with start_line parameter
5. Break large changes into smaller, more specific diffs
${errorDetails ? `\nTechnical details:\n${errorDetails}\n` : ""}
</error_details>\n\n`
					}
				} else {
					// Get the content from the result and update success count
					originalContent = diffResult.content || originalContent
					successCount = diffItems.length - (diffResult.failParts?.length || 0)
				}

				// If no diffs were successfully applied, continue to next file
				if (successCount === 0) {
					if (formattedError) {
						const currentCount = cline.consecutiveMistakeCountForApplyDiff.get(relPath) || 0
						if (currentCount >= 2) {
							await cline.say("diff_error", formattedError)
						}
						cline.recordToolError("apply_diff", formattedError)
						results.push(formattedError)

						// For single file operations, we need to send a complete message to stop the spinner
						if (operationsToApprove.length === 1) {
							const sharedMessageProps: ClineSayTool = {
								tool: "appliedDiff",
								path: getReadablePath(cline.cwd, relPath),
								diff: diffItems.map((item) => item.content).join("\n\n"),
							}
							// Send a complete message (partial: false) to update the UI and stop the spinner
							await cline.ask("tool", JSON.stringify(sharedMessageProps), false).catch(() => {})
						}
					}
					continue
				}

				cline.consecutiveMistakeCount = 0
				cline.consecutiveMistakeCountForApplyDiff.delete(relPath)

				// Check if preventFocusDisruption experiment is enabled
				const provider = cline.providerRef.deref()
				const state = await provider?.getState()
				const diagnosticsEnabled = state?.diagnosticsEnabled ?? true
				const writeDelayMs = state?.writeDelayMs ?? DEFAULT_WRITE_DELAY_MS
				const isPreventFocusDisruptionEnabled = experiments.isEnabled(
					state?.experiments ?? {},
					EXPERIMENT_IDS.PREVENT_FOCUS_DISRUPTION,
				)

				// For batch operations, we've already gotten approval
				const isWriteProtected = cline.rooProtectedController?.isWriteProtected(relPath) || false
				const sharedMessageProps: ClineSayTool = {
					tool: "appliedDiff",
					path: getReadablePath(cline.cwd, relPath),
					isProtected: isWriteProtected,
				}

				// If single file, handle based on PREVENT_FOCUS_DISRUPTION setting
				let didApprove = true
				if (operationsToApprove.length === 1) {
					// Prepare common data for single file operation
					const diffContents = diffItems.map((item) => item.content).join("\n\n")
					const operationMessage = JSON.stringify({
						...sharedMessageProps,
						diff: diffContents,
					} satisfies ClineSayTool)

					let toolProgressStatus
					if (cline.diffStrategy && cline.diffStrategy.getProgressStatus) {
						toolProgressStatus = cline.diffStrategy.getProgressStatus(
							{
								...block,
								params: { ...block.params, diff: diffContents },
							},
							{ success: true },
						)
					}

					// Set up diff view
					cline.diffViewProvider.editType = "modify"

					// Show diff view if focus disruption prevention is disabled
					if (!isPreventFocusDisruptionEnabled) {
						await cline.diffViewProvider.open(relPath)
						await cline.diffViewProvider.update(originalContent!, true)
						cline.diffViewProvider.scrollToFirstDiff()
					} else {
						// For direct save, we still need to set originalContent
						cline.diffViewProvider.originalContent = await fs.readFile(absolutePath, "utf-8")
					}

					// Ask for approval (same for both flows)
					const isWriteProtected = cline.rooProtectedController?.isWriteProtected(relPath) || false
					didApprove = await askApproval("tool", operationMessage, toolProgressStatus, isWriteProtected)

					if (!didApprove) {
						// Revert changes if diff view was shown
						if (!isPreventFocusDisruptionEnabled) {
							await cline.diffViewProvider.revertChanges()
						}
						results.push(`Changes to ${relPath} were not approved by user`)
						continue
					}

					// Save the changes
					if (isPreventFocusDisruptionEnabled) {
						// Direct file write without diff view or opening the file
						await cline.diffViewProvider.saveDirectly(
							relPath,
							originalContent!,
							false,
							diagnosticsEnabled,
							writeDelayMs,
						)
					} else {
						// Call saveChanges to update the DiffViewProvider properties
						await cline.diffViewProvider.saveChanges(diagnosticsEnabled, writeDelayMs)
					}
				} else {
					// Batch operations - already approved above
					if (isPreventFocusDisruptionEnabled) {
						// Direct file write without diff view or opening the file
						cline.diffViewProvider.editType = "modify"
						cline.diffViewProvider.originalContent = await fs.readFile(absolutePath, "utf-8")
						await cline.diffViewProvider.saveDirectly(
							relPath,
							originalContent!,
							false,
							diagnosticsEnabled,
							writeDelayMs,
						)
					} else {
						// Original behavior with diff view
						cline.diffViewProvider.editType = "modify"
						await cline.diffViewProvider.open(relPath)
						await cline.diffViewProvider.update(originalContent!, true)
						cline.diffViewProvider.scrollToFirstDiff()

						// Call saveChanges to update the DiffViewProvider properties
						await cline.diffViewProvider.saveChanges(diagnosticsEnabled, writeDelayMs)
					}
				}

				// Track file edit operation
				await cline.fileContextTracker.trackFileContext(relPath, "roo_edited" as RecordSource)

				// Used to determine if we should wait for busy terminal to update before sending api request
				cline.didEditFile = true
				let partFailHint = ""

				if (successCount < diffItems.length) {
					partFailHint = `Unable to apply all diff parts to file: ${absolutePath}`
				}

				// Get the formatted response message
				const message = await cline.diffViewProvider.pushToolWriteResult(cline, cline.cwd, !fileExists)

				if (partFailHint) {
					results.push(partFailHint + "\n" + message)
					// Record partial success
					cline.recordAction(
						`apply_diff: ${relPath}`,
						false,
						`Partial success: ${partFailHint}`,
						'apply_diff',
						{ filePath: relPath, diffCount: diffItems.length, successCount }
					)
				} else {
					results.push(message)
					// Record successful operation
					cline.recordAction(
						`apply_diff: ${relPath}`,
						true,
						undefined,
						'apply_diff',
						{ filePath: relPath, diffCount: diffItems.length, successCount }
					)
				}

				await cline.diffViewProvider.reset()
			} catch (error) {
				const errorMsg = error instanceof Error ? error.message : String(error)
				updateOperationResult(relPath, {
					status: "error",
					error: `Error processing ${relPath}: ${errorMsg}`,
				})
				results.push(`Error processing ${relPath}: ${errorMsg}`)

				// Record failed operation
				cline.recordAction(
					`apply_diff: ${relPath}`,
					false,
					errorMsg,
					'apply_diff',
					{ filePath: relPath, error: errorMsg }
				)
			}
		}

		// Add filtered operation errors to results
		if (filteredOperationErrors.length > 0) {
			results.push(...filteredOperationErrors)
		}

		// Report all diff errors at once if any
		if (allDiffErrors.length > 0) {
			await cline.say("diff_error", allDiffErrors.join("\n"))
		}

		// Check for single SEARCH/REPLACE block warning
		let totalSearchBlocks = 0
		for (const operation of operations) {
			for (const diffItem of operation.diff) {
				const searchBlocks = (diffItem.content.match(/<<<<<<< SEARCH/g) || []).length
				totalSearchBlocks += searchBlocks
			}
		}

		const singleBlockNotice =
			totalSearchBlocks === 1
				? "\n<notice>Making multiple related changes in a single apply_diff is more efficient. If other changes are needed in this file, please include them as additional SEARCH/REPLACE blocks.</notice>"
				: ""

		// Push the final result combining all operation results
		pushToolResult(results.join("\n\n") + singleBlockNotice)
		return
	} catch (error) {
		await handleError("applying diff", error)
		await cline.diffViewProvider.reset()
		return
	}
}
