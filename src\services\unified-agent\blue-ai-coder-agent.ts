import * as vscode from "vscode"
import { AugmentContextManager } from "../blue-ai-context/manager"
import { MCPIntegrationService } from "../mcp/integration"
import { MemoryManager, MemoryContext, MemoryScope } from "../agent-memories"
import { SmartTerminalIntegration } from "../smart-terminal-cache/terminal-integration"
import { RoutingEngine } from "../intelligent-routing"

/**
 * Unified BLUE AI CODER Agent
 * Combines all specialized agent capabilities into one intelligent system
 */
export class BlueAiCoderAgent {
	private static instance: BlueAiCoderAgent | null = null
	private contextManager: AugmentContextManager
	private mcpIntegration: MCPIntegrationService
	private memoryManager: MemoryManager
	private terminalIntegration: SmartTerminalIntegration
	private routingService: RoutingEngine
	private isInitialized = false

	private constructor(private context: vscode.ExtensionContext) {
		this.contextManager = new AugmentContextManager({
			embeddingApiKey: process.env.OPENAI_API_KEY,
			embeddingModelId: "text-embedding-3-small"
		})
		this.mcpIntegration = new MCPIntegrationService()
		this.memoryManager = new MemoryManager({
			enableAutoLearning: true,
			adaptationEnabled: true,
		})
		this.terminalIntegration = SmartTerminalIntegration.getInstance(context)
		this.routingService = new RoutingEngine()
	}

	public static getInstance(context?: vscode.ExtensionContext): BlueAiCoderAgent {
		if (!BlueAiCoderAgent.instance) {
			if (!context) {
				throw new Error("Context required for first initialization")
			}
			BlueAiCoderAgent.instance = new BlueAiCoderAgent(context)
		}
		return BlueAiCoderAgent.instance
	}

	public async initialize(): Promise<void> {
		if (this.isInitialized) return

		console.log("[BlueAiCoderAgent] Initializing unified agent system...")

		// Initialize all subsystems
		await Promise.all([
			this.contextManager.initialize(),
			this.mcpIntegration.initialize(),
			this.memoryManager.initialize(),
			this.terminalIntegration.initialize()
		])

		this.setupAgentCapabilities()
		this.isInitialized = true

		console.log("[BlueAiCoderAgent] Unified agent system initialized successfully")
	}

	/**
	 * Main agent processing method - handles all types of requests
	 */
	public async processRequest(request: AgentRequest): Promise<AgentResponse> {
		console.log(`[BlueAiCoderAgent] Processing request: ${request.type}`)

		// Analyze request and determine optimal approach
		const analysis = await this.analyzeRequest(request)
		
		// Retrieve relevant context
		const context = await this.gatherContext(request, analysis)
		
		// Apply personalization from memories
		const personalizedContext = await this.applyPersonalization(context, request)
		
		// Route to appropriate processing method
		const response = await this.routeAndProcess(request, personalizedContext, analysis)
		
		// Learn from the interaction
		await this.learnFromInteraction(request, response, context)
		
		return response
	}

	/**
	 * Enhanced code analysis with full context awareness
	 */
	public async analyzeCode(filePath: string, query?: string): Promise<CodeAnalysis> {
		const context = await this.contextManager.retrieveEnhancedContext({
			query: query || `analyze code in ${filePath}`,
			maxResults: 50,
			includeCommitHistory: true,
			directoryPrefix: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
		})

		const memories = await this.memoryManager.getRelevantMemories({
			scope: MemoryScope.PROJECT,
			workspace: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
		}, 10)

		return {
			filePath,
			context: context.codeContext.map((c: any) => ({
				content: c.content,
				relevance: c.score,
				type: c.type
			})),
			insights: memories.map((m: any) => m.content),
			recommendations: await this.generateRecommendations(context.codeContext, memories),
			complexity: this.assessComplexity(context.codeContext),
			dependencies: await this.analyzeDependencies(filePath, context.codeContext)
		}
	}

	/**
	 * Intelligent terminal command execution with caching
	 */
	public async executeTerminalCommand(
		command: string, 
		workingDir?: string,
		options?: TerminalExecutionOptions
	): Promise<TerminalExecutionResult> {
		// Use smart terminal integration for caching
		const terminal = await this.getOrCreateTerminal(workingDir)
		
		const callbacks = {
			onLine: (line: string) => {
				if (options?.onOutput) {
					options.onOutput(line)
				}
			},
			onCompleted: (output: string) => {
				console.log(`[BlueAiCoderAgent] Command completed: ${command}`)
			},
			onError: (error: Error) => {
				console.error(`[BlueAiCoderAgent] Command failed: ${command}`, error)
			}
		}

		const output = await this.terminalIntegration.executeCommandWithSmartCache(
			terminal,
			command,
			callbacks
		)

		return {
			command,
			output,
			exitCode: 0, // Simplified for now
			cached: false, // Will be set by integration
			executionTime: 0 // Will be measured by integration
		}
	}

	/**
	 * Advanced problem solving with multi-step reasoning
	 */
	public async solveProblem(problem: ProblemDescription): Promise<ProblemSolution> {
		// Break down the problem
		const breakdown = await this.breakdownProblem(problem)
		
		// Gather relevant context and knowledge
		const context = await this.gatherProblemContext(problem, breakdown)
		
		// Generate multiple solution approaches
		const approaches = await this.generateSolutionApproaches(problem, context, breakdown)
		
		// Evaluate and select best approach
		const selectedApproach = await this.selectBestApproach(approaches, context)
		
		// Execute the solution
		const solution = await this.executeSolution(selectedApproach, context)
		
		// Validate and refine
		const validatedSolution = await this.validateSolution(solution, problem)
		
		return validatedSolution
	}

	/**
	 * Comprehensive project understanding
	 */
	public async understandProject(): Promise<ProjectUnderstanding> {
		const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
		if (!workspaceRoot) {
			throw new Error("No workspace folder found")
		}

		// Get comprehensive project context
		const context = await this.contextManager.retrieveEnhancedContext({
			query: "project structure architecture dependencies",
			maxResults: 100,
			includeCommitHistory: true,
			directoryPrefix: workspaceRoot
		})

		// Analyze project memories
		const memories = await this.memoryManager.getRelevantMemories({
			scope: MemoryScope.PROJECT,
			workspace: workspaceRoot
		}, 20)

		// MCP integration is available for tool execution
		const mcpAvailable = this.mcpIntegration.initialized

		return {
			structure: await this.analyzeProjectStructure(context.codeContext),
			technologies: await this.identifyTechnologies(context.codeContext),
			patterns: await this.identifyPatterns(context.codeContext, memories),
			dependencies: await this.analyzeDependencyGraph(context.codeContext),
			complexity: this.assessProjectComplexity(context.codeContext),
			recommendations: await this.generateProjectRecommendations(context.codeContext, memories),
			mcpCapabilities: mcpAvailable ? ["MCP tools available"] : []
		}
	}

	private async analyzeRequest(request: AgentRequest): Promise<RequestAnalysis> {
		// Determine request complexity and type
		const complexity = this.assessRequestComplexity(request)
		const category = this.categorizeRequest(request)
		const requiredCapabilities = this.identifyRequiredCapabilities(request)

		return {
			complexity,
			category,
			requiredCapabilities,
			estimatedTime: this.estimateProcessingTime(complexity, requiredCapabilities),
			recommendedModel: (await this.routingService.route({
				systemPrompt: `Handle ${category} request with complexity ${complexity}`,
				userMessage: request.content,
				contextFiles: []
			})).executionPlan.primaryExecution.modelId
		}
	}

	private async gatherContext(request: AgentRequest, analysis: RequestAnalysis): Promise<AgentContext> {
		const contextQueries = this.generateContextQueries(request, analysis)
		
		const contextResults = await Promise.all(
			contextQueries.map(query =>
				this.contextManager.retrieveEnhancedContext({
					query,
					maxResults: 20,
					includeCommitHistory: true
				})
			)
		)

		const flattenedContext = contextResults.flatMap(result => [
			...result.codeContext,
			...result.commitContext
		])

		return {
			codeContext: contextResults.flatMap(result => result.codeContext),
			commitHistory: contextResults.flatMap(result => result.commitContext),
			documentation: flattenedContext.filter((c: any) => c.type === "documentation"),
			relevanceScore: this.calculateOverallRelevance(flattenedContext)
		}
	}

	private async applyPersonalization(context: AgentContext, request: AgentRequest): Promise<AgentContext> {
		const userMemories = await this.memoryManager.getRelevantMemories({
			scope: MemoryScope.GLOBAL,
			workspace: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
		}, 10)

		const projectMemories = await this.memoryManager.getRelevantMemories({
			scope: MemoryScope.PROJECT,
			workspace: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
		}, 15)

		// Apply personalization based on memories
		const personalizedContext = {
			...context,
			userPreferences: userMemories.map((m: any) => m.content),
			projectPatterns: projectMemories.map((m: any) => m.content),
			adaptations: await this.generateAdaptations(userMemories, projectMemories)
		}

		return personalizedContext
	}

	private async routeAndProcess(
		request: AgentRequest, 
		context: AgentContext, 
		analysis: RequestAnalysis
	): Promise<AgentResponse> {
		// Route based on request category and complexity
		switch (analysis.category) {
			case "code-analysis":
				return this.processCodeAnalysisRequest(request, context)
			case "code-generation":
				return this.processCodeGenerationRequest(request, context)
			case "debugging":
				return this.processDebuggingRequest(request, context)
			case "refactoring":
				return this.processRefactoringRequest(request, context)
			case "terminal-command":
				return this.processTerminalCommandRequest(request, context)
			case "project-understanding":
				return this.processProjectUnderstandingRequest(request, context)
			case "problem-solving":
				return this.processProblemSolvingRequest(request, context)
			default:
				return this.processGeneralRequest(request, context)
		}
	}

	private setupAgentCapabilities(): void {
		// Register all available capabilities
		console.log("[BlueAiCoderAgent] Setting up unified capabilities:")
		console.log("  ✓ Advanced context retrieval and workspace indexing")
		console.log("  ✓ Intelligent model routing and scaling")
		console.log("  ✓ Smart terminal caching and execution")
		console.log("  ✓ MCP integration for extended capabilities")
		console.log("  ✓ Agent memories for personalization")
		console.log("  ✓ Multi-step problem solving")
		console.log("  ✓ Comprehensive code analysis")
		console.log("  ✓ Project understanding and recommendations")
	}

	private assessRequestComplexity(request: AgentRequest): "low" | "medium" | "high" | "very-high" {
		const indicators = {
			multiStep: request.content.includes("step") || request.content.includes("then"),
			codeModification: request.content.includes("change") || request.content.includes("modify"),
			analysis: request.content.includes("analyze") || request.content.includes("understand"),
			creation: request.content.includes("create") || request.content.includes("build"),
			debugging: request.content.includes("debug") || request.content.includes("fix")
		}

		const complexityScore = Object.values(indicators).filter(Boolean).length
		
		if (complexityScore >= 4) return "very-high"
		if (complexityScore >= 3) return "high"
		if (complexityScore >= 2) return "medium"
		return "low"
	}

	private categorizeRequest(request: AgentRequest): string {
		const content = request.content.toLowerCase()
		
		if (content.includes("analyze") || content.includes("understand")) return "code-analysis"
		if (content.includes("create") || content.includes("generate")) return "code-generation"
		if (content.includes("debug") || content.includes("fix")) return "debugging"
		if (content.includes("refactor") || content.includes("improve")) return "refactoring"
		if (content.includes("run") || content.includes("execute")) return "terminal-command"
		if (content.includes("project") || content.includes("overview")) return "project-understanding"
		if (content.includes("solve") || content.includes("problem")) return "problem-solving"
		
		return "general"
	}

	private identifyRequiredCapabilities(request: AgentRequest): string[] {
		const capabilities: string[] = []
		const content = request.content.toLowerCase()

		if (content.includes("file") || content.includes("code")) capabilities.push("file-access")
		if (content.includes("run") || content.includes("command")) capabilities.push("terminal")
		if (content.includes("web") || content.includes("browser")) capabilities.push("browser")
		if (content.includes("git") || content.includes("commit")) capabilities.push("version-control")
		if (content.includes("install") || content.includes("package")) capabilities.push("package-management")

		return capabilities
	}

	private estimateProcessingTime(complexity: string, capabilities: string[]): number {
		const baseTime = {
			"low": 1000,
			"medium": 3000,
			"high": 8000,
			"very-high": 15000
		}[complexity] || 1000

		const capabilityMultiplier = 1 + (capabilities.length * 0.2)
		return Math.round(baseTime * capabilityMultiplier)
	}

	// Placeholder methods for processing different request types
	private async processCodeAnalysisRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "code-analysis", content: "Code analysis completed", confidence: 0.9 }
	}

	private async processCodeGenerationRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "code-generation", content: "Code generated", confidence: 0.85 }
	}

	private async processDebuggingRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "debugging", content: "Debug analysis completed", confidence: 0.8 }
	}

	private async processRefactoringRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "refactoring", content: "Refactoring suggestions provided", confidence: 0.85 }
	}

	private async processTerminalCommandRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "terminal-command", content: "Command executed", confidence: 0.95 }
	}

	private async processProjectUnderstandingRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "project-understanding", content: "Project analysis completed", confidence: 0.9 }
	}

	private async processProblemSolvingRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "problem-solving", content: "Problem solution provided", confidence: 0.8 }
	}

	private async processGeneralRequest(request: AgentRequest, context: AgentContext): Promise<AgentResponse> {
		return { type: "general", content: "Request processed", confidence: 0.75 }
	}

	// Additional helper methods would be implemented here...
	private generateContextQueries(request: AgentRequest, analysis: RequestAnalysis): string[] {
		return [request.content] // Simplified
	}

	private calculateOverallRelevance(context: any[]): number {
		return context.reduce((sum, c) => sum + c.score, 0) / context.length || 0
	}

	private async generateAdaptations(userMemories: any[], projectMemories: any[]): Promise<string[]> {
		return [] // Placeholder
	}

	private async learnFromInteraction(request: AgentRequest, response: AgentResponse, context: AgentContext): Promise<void> {
		// Learn from the interaction for future improvements
		await this.memoryManager.learnFromInteraction(
			`${request.type}: ${request.content}`,
			{
				scope: MemoryScope.PROJECT,
				workspace: vscode.workspace.workspaceFolders?.[0]?.uri.fsPath
			},
			{
				request: request.content,
				response: response.content,
				confidence: response.confidence,
				type: request.type
			},
			"positive" // Assume positive feedback for now
		)
	}

	private async getOrCreateTerminal(workingDir?: string): Promise<any> {
		// Simplified terminal creation
		return { id: 1, getCurrentWorkingDirectory: () => workingDir || process.cwd() }
	}

	// Additional placeholder methods...
	private async breakdownProblem(problem: ProblemDescription): Promise<any> { return {} }
	private async gatherProblemContext(problem: ProblemDescription, breakdown: any): Promise<any> { return {} }
	private async generateSolutionApproaches(problem: ProblemDescription, context: any, breakdown: any): Promise<any[]> { return [] }
	private async selectBestApproach(approaches: any[], context: any): Promise<any> { return {} }
	private async executeSolution(approach: any, context: any): Promise<any> { return {} }
	private async validateSolution(solution: any, problem: ProblemDescription): Promise<ProblemSolution> { return {} as any }
	private async analyzeProjectStructure(context: any[]): Promise<any> { return {} }
	private async identifyTechnologies(context: any[]): Promise<string[]> { return [] }
	private async identifyPatterns(context: any[], memories: any[]): Promise<string[]> { return [] }
	private async analyzeDependencyGraph(context: any[]): Promise<any> { return {} }
	private assessProjectComplexity(context: any[]): string { return "medium" }
	private async generateProjectRecommendations(context: any[], memories: any[]): Promise<string[]> { return [] }
	private async generateRecommendations(context: any[], memories: any[]): Promise<string[]> { return [] }
	private assessComplexity(context: any[]): string { return "medium" }
	private async analyzeDependencies(filePath: string, context: any[]): Promise<string[]> { return [] }
}

// Type definitions
interface AgentRequest {
	type: string
	content: string
	context?: any
}

interface AgentResponse {
	type: string
	content: string
	confidence: number
	metadata?: any
}

interface RequestAnalysis {
	complexity: "low" | "medium" | "high" | "very-high"
	category: string
	requiredCapabilities: string[]
	estimatedTime: number
	recommendedModel: any
}

interface AgentContext {
	codeContext: any[]
	commitHistory: any[]
	documentation: any[]
	relevanceScore: number
	userPreferences?: string[]
	projectPatterns?: string[]
	adaptations?: string[]
}

interface CodeAnalysis {
	filePath: string
	context: any[]
	insights: string[]
	recommendations: string[]
	complexity: string
	dependencies: string[]
}

interface TerminalExecutionOptions {
	onOutput?: (line: string) => void
	timeout?: number
}

interface TerminalExecutionResult {
	command: string
	output: string
	exitCode: number
	cached: boolean
	executionTime: number
}

interface ProblemDescription {
	title: string
	description: string
	context?: any
}

interface ProblemSolution {
	approach: string
	steps: string[]
	code?: string
	explanation: string
	confidence: number
}

interface ProjectUnderstanding {
	structure: any
	technologies: string[]
	patterns: string[]
	dependencies: any
	complexity: string
	recommendations: string[]
	mcpCapabilities: string[]
}
